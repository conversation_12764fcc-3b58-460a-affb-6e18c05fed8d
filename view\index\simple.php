<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?php
    // 构建页面标题
    $pageTitle = htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育');
    if (!empty($settings['site_subtitle'])) {
        $pageTitle .= ' - ' . htmlspecialchars($settings['site_subtitle']);
    }
    ?>
    <title><?php echo $pageTitle; ?></title>

    <?php
    // 构建SEO描述，避免重复
    $siteDescription = htmlspecialchars($settings['site_description'] ?? '电子资料兑换系统');
    $siteKeywords = htmlspecialchars($settings['site_keywords'] ?? '卡密,兑换,资料,教程');
    $siteUrl = htmlspecialchars($settings['site_url'] ?? 'https://kmdh.hzoedu.com');
    ?>

    <!-- SEO Meta标签 -->
    <meta name="description" content="<?php echo $siteDescription; ?>">
    <meta name="keywords" content="<?php echo $siteKeywords; ?>">
    <meta name="author" content="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>">

    <!-- Open Graph Meta标签 -->
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="<?php echo $siteDescription; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo $siteUrl; ?>">
    <?php if (!empty($settings['site_logo'])): ?>
    <meta property="og:image" content="<?php echo htmlspecialchars($settings['site_logo']); ?>">
    <?php endif; ?>

    <!-- Twitter Card Meta标签 -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="<?php echo $pageTitle; ?>">
    <meta name="twitter:description" content="<?php echo $siteDescription; ?>">
    <?php if (!empty($settings['site_logo'])): ?>
    <meta name="twitter:image" content="<?php echo htmlspecialchars($settings['site_logo']); ?>">
    <?php endif; ?>

    <link href="/static/css/user.css" rel="stylesheet">
    <link href="/static/css/query-modal.css" rel="stylesheet">
    <link href="/static/css/alert-modal.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Logo区域 -->
        <?php if (!empty($settings['site_logo'])): ?>
        <div class="logo-container">
            <img src="<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>" class="site-logo">
        </div>
        <?php endif; ?>

        <h1><?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?></h1>
        <?php if (!empty($settings['site_subtitle'])): ?>
        <div class="site-subtitle"><?php echo htmlspecialchars($settings['site_subtitle']); ?></div>
        <?php endif; ?>
        
        <div class="input-flex">
            <input type="text" id="cardNumber" placeholder="请输入卡密" />
            <button onclick="exchange()">立即兑换</button>
        </div>

        <button onclick="showQueryModal()" class="query-button">兑换记录查询</button>

        <div id="result" class="result-area"></div>
        
        <div class="note">
            <strong>说明：</strong> <?php echo htmlspecialchars($settings['page_footer_notice'] ?? '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'); ?>
        </div>
    </div>

    <!-- 页面底部信息 -->
    <footer class="site-footer">
        <div class="footer-content">
            <?php if (!empty($settings['copyright'])): ?>
            <div class="copyright">
                <?php echo htmlspecialchars($settings['copyright']); ?>
            </div>
            <?php else: ?>
            <div class="copyright">
                © 2024 红嘴鸥教育
            </div>
            <?php endif; ?>

            <?php if (!empty($settings['icp_number'])): ?>
            <div class="icp-info">
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
                    <?php echo htmlspecialchars($settings['icp_number']); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </footer>

    <!-- 查询弹窗 -->
    <div id="queryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>兑换记录查询</h3>
                <span class="close" onclick="closeQueryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>请输入要查询的卡密：</p>
                <input type="text" id="queryCardNumber" placeholder="请输入卡密" />
                <div class="modal-buttons">
                    <button onclick="queryExchangeRecord()" class="btn-primary">查询</button>
                    <button onclick="closeQueryModal()" class="btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/alert-modal.js"></script>
    <script src="/static/js/query-modal.js"></script>
    <script src="/static/js/exchange.js"></script>
</body>
</html>
