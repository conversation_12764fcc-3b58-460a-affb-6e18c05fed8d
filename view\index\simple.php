<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红嘴鸥教育 - 电子资料兑换系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 { color: #333; margin-bottom: 10px; }
        h2 { color: #666; margin-bottom: 30px; }
        input { 
            width: 70%; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 10px 0 0 10px;
            font-size: 16px;
        }
        button { 
            width: 25%; 
            padding: 15px; 
            background: #ff6b6b; 
            color: white; 
            border: none; 
            border-radius: 0 10px 10px 0;
            font-size: 16px;
            cursor: pointer;
        }
        .note {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #666;
            font-size: 14px;
        }

        /* 内容显示样式 */

        .download-btn {
            display: inline-block;
            padding: 10px 25px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white !important;
            text-decoration: none !important;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: linear-gradient(45deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            color: white !important;
        }

        .contact-info {
            color: #dc3545 !important;
            font-weight: bold !important;
            background: rgba(220, 53, 69, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        /* 链接容器样式 */
        .link-container {
            display: inline-block;
            margin: 2px 0;
            vertical-align: baseline;
            max-width: 100%;
            word-break: break-all;
            word-wrap: break-word;
        }

        /* 内容链接样式 */
        .content-link {
            color: #007bff !important;
            text-decoration: underline !important;
            font-weight: 500;
            line-height: 1.5;
            vertical-align: baseline;
            display: inline;
            word-break: break-all;
            word-wrap: break-word;
        }
        .content-link:hover {
            color: #0056b3 !important;
            text-decoration: none !important;
        }

        /* 复制按钮样式 */
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 12px;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.2s ease;
            vertical-align: baseline;
            white-space: nowrap;
            line-height: 1;
            height: auto;
            display: inline-block;
            position: relative;
            top: 0;
            margin-left: 5px;
        }
        .copy-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }
        .copy-btn:active {
            transform: scale(0.95);
        }

        /* 复制成功提示样式 */
        .copy-success {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            font-weight: bold;
            animation: fadeInOut 2s ease-in-out;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        }

        /* 推广模块样式 */
        .promotion-module {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .promotion-title {
            color: #dc3545;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .promotion-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .promotion-btn {
            background: #6f42c1;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-block;
        }

        .promotion-btn:hover {
            background: #5a32a3;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
            color: white;
            text-decoration: none;
        }

        .promotion-contact {
            color: #dc3545;
            font-size: 14px;
            font-weight: bold;
            margin-top: 10px;
        }

        .promotion-contact-value {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>红嘴鸥教育</h1>
        <h2>电子资料兑换系统</h2>
        
        <div style="display: flex; margin-bottom: 20px;">
            <input type="text" id="cardNumber" placeholder="请输入卡密" />
            <button onclick="exchange()">立即兑换</button>
        </div>
        
        <button onclick="query()" style="width: 200px; border-radius: 25px; background: #00d2ff;">兑换记录查询</button>
        
        <div id="result" style="margin-top: 20px;"></div>
        
        <div class="note">
            <strong>说明：</strong> 此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，
            点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888
        </div>
    </div>

    <script>
        function exchange() {
            console.log('兑换函数被调用');
            const cardNumber = document.getElementById('cardNumber').value;
            console.log('输入的卡密:', cardNumber);

            if (!cardNumber) {
                alert('请输入卡密');
                return;
            }

            console.log('准备发送请求到 /exchange');
            fetch('/exchange', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'card_number=' + encodeURIComponent(cardNumber)
            })
            .then(response => {
                console.log('收到响应，状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('解析后的数据:', data);

                if (data.code === 1) {
                    // 兑换成功，显示详细信息
                    let html = '<div style="padding: 20px; margin-top: 15px; border-radius: 10px; background: #d4edda; color: #155724;">';
                    html += '<h4 style="margin: 0 0 15px 0; color: #155724; text-align: center;">🎉 ' + data.msg + '</h4>';

                    if (data.data) {
                        if (data.data.exchange_time) {
                            html += '<p style="margin: 8px 0; text-align: center;"><strong>兑换时间：</strong>' + data.data.exchange_time + '</p>';
                        }

                        // 显示兑换的内容
                        if (data.data.contents && data.data.contents.length > 0) {
                            html += '<div style="margin-top: 15px;">';
                            html += '<h5 style="margin: 0 0 15px 0; color: #155724; text-align: center;">📚 兑换内容</h5>';

                            data.data.contents.forEach(function(content) {
                                // 内容标题
                                html += '<div style="margin: 15px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; text-align: center;">';
                                html += '<h4 style="margin: 0 0 15px 0; color: #856404; font-weight: bold;">' + content.title + '</h4>';

                                // 显示详细内容 (content字段)
                                if (content.content) {
                                    // 将换行符转换为<br>，并保持原有格式
                                    let formattedContent = content.content
                                        .replace(/\n/g, '<br>')
                                        .replace(/\r/g, '');

                                    // 使用更精确的方法处理链接
                                    // 先标记已处理的链接，避免重复处理
                                    let linkCounter = 0;
                                    const linkPlaceholders = {};

                                    // 处理带协议的链接
                                    formattedContent = formattedContent.replace(/(https?:\/\/[^\s<>\[\]]+)/gi, function(match) {
                                        const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
                                        linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + match + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + match + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
                                        return placeholder;
                                    });

                                    // 处理不带协议的网址
                                    formattedContent = formattedContent.replace(/\b((?:www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*\.[a-zA-Z]{2,}(?:\/[^\s<>\[\]]*)?)\b/gi, function(match) {
                                        // 检查是否已经在占位符中
                                        if (match.includes('___LINK_PLACEHOLDER_')) return match;

                                        const fullUrl = match.startsWith('www.') ? 'https://' + match : 'https://' + match;
                                        const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
                                        linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + fullUrl + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + fullUrl + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
                                        return placeholder;
                                    });

                                    // 恢复链接占位符
                                    for (const placeholder in linkPlaceholders) {
                                        formattedContent = formattedContent.replace(placeholder, linkPlaceholders[placeholder]);
                                    }

                                    // 处理其他高亮内容
                                    formattedContent = formattedContent
                                        // 识别微信号并高亮显示
                                        .replace(/(微信[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>')
                                        // 识别QQ号并高亮显示
                                        .replace(/(QQ[：:]?\s*\d+)/gi, '<span class="contact-info">$1</span>')
                                        // 识别其他联系方式
                                        .replace(/(客服[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>');

                                    html += '<div style="background: #fffbf0; padding: 15px; border-radius: 6px; margin: 10px 0; text-align: left; line-height: 1.8; border: 1px solid #f1c40f; word-wrap: break-word; word-break: break-all; overflow-wrap: break-word;">';
                                    html += formattedContent;
                                    html += '</div>';
                                }

                                // 显示描述（如果有）
                                if (content.description && content.description !== content.title) {
                                    html += '<div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 5px; font-style: italic; color: #666;">';
                                    html += content.description;
                                    html += '</div>';
                                }

                                html += '</div>';
                            });

                            html += '</div>';
                        }
                    }

                    html += '</div>';

                    // 添加推广模块
                    if (data.data.promotion && data.data.promotion.enabled === '1') {
                        console.log('推广模块已启用，生成推广内容');
                        const promotionHtml = generatePromotionModule(data.data.promotion);
                        if (promotionHtml && promotionHtml.trim() !== '') {
                            html += promotionHtml;
                        }
                    } else {
                        console.log('推广模块未启用或配置为空');
                    }

                    document.getElementById('result').innerHTML = html;
                } else {
                    // 兑换失败
                    document.getElementById('result').innerHTML =
                        '<div style="padding: 15px; margin-top: 15px; border-radius: 10px; background: #f8d7da; color: #721c24;">' + data.msg + '</div>';
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                document.getElementById('result').innerHTML =
                    '<div style="padding: 15px; margin-top: 15px; border-radius: 10px; background: #f8d7da; color: #721c24;">网络错误: ' + error.message + '</div>';
            });
        }
        
        function query() {
            console.log('查询函数被调用');
            const cardNumber = document.getElementById('cardNumber').value;
            console.log('查询的卡密:', cardNumber);

            if (!cardNumber) {
                alert('请输入卡密');
                return;
            }

            console.log('准备发送查询请求到 /query');
            fetch('/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'card_number=' + encodeURIComponent(cardNumber)
            })
            .then(response => {
                console.log('查询响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('查询解析后的数据:', data);

                if (data.code === 1) {
                    // 查询成功，显示详细信息
                    let html = '<div style="padding: 20px; margin-top: 15px; border-radius: 10px; background: #d1ecf1; color: #0c5460;">';
                    html += '<h4 style="margin: 0 0 15px 0; color: #0c5460;">📋 查询结果</h4>';

                    if (data.data && data.data.card_info) {
                        const card = data.data.card_info;
                        html += '<div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px; margin: 10px 0;">';
                        html += '<h6 style="margin: 0 0 10px 0; color: #0c5460;">卡密信息</h6>';

                        if (card.card_type) {
                            html += '<p style="margin: 5px 0;"><strong>类型：</strong>' + card.card_type + '</p>';
                        }
                        if (card.value) {
                            html += '<p style="margin: 5px 0;"><strong>面值：</strong>￥' + card.value + '</p>';
                        }
                        if (card.status) {
                            const statusColor = card.status === '已使用' ? '#dc3545' : card.status === '未使用' ? '#28a745' : '#ffc107';
                            html += '<p style="margin: 5px 0;"><strong>状态：</strong><span style="color: ' + statusColor + '; font-weight: bold;">' + card.status + '</span></p>';
                        }
                        if (card.expire_time) {
                            html += '<p style="margin: 5px 0;"><strong>有效期：</strong>' + card.expire_time + '</p>';
                        }
                        if (card.used_time) {
                            html += '<p style="margin: 5px 0;"><strong>使用时间：</strong>' + card.used_time + '</p>';
                        }

                        html += '</div>';
                    }

                    // 显示兑换记录
                    if (data.data && data.data.exchange_records && data.data.exchange_records.length > 0) {
                        html += '<div style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px; margin: 10px 0;">';
                        html += '<h6 style="margin: 0 0 10px 0; color: #0c5460;">兑换记录</h6>';
                        data.data.exchange_records.forEach(function(record) {
                            html += '<div style="margin: 8px 0; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #17a2b8;">';
                            html += '<p style="margin: 2px 0; font-size: 14px;"><strong>时间：</strong>' + record.exchange_time + '</p>';
                            html += '<p style="margin: 2px 0; font-size: 14px;"><strong>状态：</strong>' + (record.status === 1 ? '成功' : '失败') + '</p>';
                            if (record.remark) {
                                html += '<p style="margin: 2px 0; font-size: 12px; color: #666;"><strong>备注：</strong>' + record.remark + '</p>';
                            }
                            html += '</div>';
                        });
                        html += '</div>';
                    }

                    html += '</div>';
                    document.getElementById('result').innerHTML = html;
                } else {
                    // 查询失败
                    document.getElementById('result').innerHTML =
                        '<div style="padding: 15px; margin-top: 15px; border-radius: 10px; background: #f8d7da; color: #721c24;">' + data.msg + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = 
                    '<div style="padding: 15px; margin-top: 15px; border-radius: 10px; background: #f8d7da; color: #721c24;">网络错误</div>';
            });
        }

        // 生成推广模块HTML
        function generatePromotionModule(promotion) {
            console.log('生成推广模块，配置:', promotion);

            let html = '<div class="promotion-module">';

            // 标题
            if (promotion.title && promotion.title.trim() !== '') {
                html += '<div class="promotion-title">' + promotion.title + '</div>';
            }

            // 按钮组
            let hasButtons = false;
            let buttonsHtml = '<div class="promotion-buttons">';

            if (promotion.buttons && Array.isArray(promotion.buttons)) {
                promotion.buttons.forEach(function(button) {
                    if (button.text && button.text.trim() !== '' && button.url && button.url.trim() !== '' && button.url !== '#') {
                        buttonsHtml += '<a href="' + button.url + '" target="_blank" class="promotion-btn" style="background: ' + (button.color || '#6f42c1') + ';">' + button.text + '</a>';
                        hasButtons = true;
                    }
                });
            }
            buttonsHtml += '</div>';

            if (hasButtons) {
                html += buttonsHtml;
            }

            // 联系方式
            if (promotion.contact && promotion.contact.text && promotion.contact.text.trim() !== '' &&
                promotion.contact.value && promotion.contact.value.trim() !== '') {
                html += '<div class="promotion-contact">' + promotion.contact.text + ' <span class="promotion-contact-value">' + promotion.contact.value + '</span></div>';
            }

            html += '</div>';

            // 如果没有任何内容，返回空字符串
            if (!hasButtons && (!promotion.title || promotion.title.trim() === '') &&
                (!promotion.contact || !promotion.contact.text || promotion.contact.text.trim() === '' ||
                 !promotion.contact.value || promotion.contact.value.trim() === '')) {
                return '';
            }

            return html;
        }

        // 复制链接功能
        function copyLink(url) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = url;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                // 尝试使用现代API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(url).then(function() {
                        showCopySuccess();
                    }).catch(function() {
                        // 如果现代API失败，使用传统方法
                        fallbackCopy();
                    });
                } else {
                    // 使用传统方法
                    fallbackCopy();
                }
            } catch (err) {
                fallbackCopy();
            }

            function fallbackCopy() {
                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showCopySuccess();
                    } else {
                        showCopyError();
                    }
                } catch (err) {
                    showCopyError();
                }
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess() {
            const successDiv = document.createElement('div');
            successDiv.className = 'copy-success';
            successDiv.innerHTML = '✅ 链接复制成功！';
            document.body.appendChild(successDiv);

            setTimeout(function() {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 2000);
        }

        // 显示复制失败提示
        function showCopyError() {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'copy-success';
            errorDiv.style.background = '#dc3545';
            errorDiv.innerHTML = '❌ 复制失败，请手动复制';
            document.body.appendChild(errorDiv);

            setTimeout(function() {
                if (document.body.contains(errorDiv)) {
                    document.body.removeChild(errorDiv);
                }
            }, 2000);
        }
    </script>
</body>
</html>
