<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红嘴鸥教育 - 电子资料兑换系统</title>
    <link href="/static/css/user.css" rel="stylesheet">
    <link href="/static/css/query-modal.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>红嘴鸥教育</h1>
        <h2>电子资料兑换系统</h2>
        
        <div class="input-flex">
            <input type="text" id="cardNumber" placeholder="请输入卡密" />
            <button onclick="exchange()">立即兑换</button>
        </div>

        <button onclick="showQueryModal()" class="query-button">兑换记录查询</button>

        <div id="result" class="result-area"></div>
        
        <div class="note">
            <strong>说明：</strong> 此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，
            点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888
        </div>
    </div>

    <!-- 查询弹窗 -->
    <div id="queryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>兑换记录查询</h3>
                <span class="close" onclick="closeQueryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>请输入要查询的卡密：</p>
                <input type="text" id="queryCardNumber" placeholder="请输入卡密" />
                <div class="modal-buttons">
                    <button onclick="queryExchangeRecord()" class="btn-primary">查询</button>
                    <button onclick="closeQueryModal()" class="btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/query-modal.js"></script>
    <script src="/static/js/exchange.js"></script>
</body>
</html>
