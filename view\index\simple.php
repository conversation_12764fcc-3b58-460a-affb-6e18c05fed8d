<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?php
    // 构建页面标题
    $pageTitle = htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育');
    if (!empty($settings['site_subtitle'])) {
        $pageTitle .= ' - ' . htmlspecialchars($settings['site_subtitle']);
    }
    ?>
    <title><?php echo $pageTitle; ?></title>

    <!-- SEO Meta标签 -->
    <meta name="description" content="<?php echo htmlspecialchars($settings['site_description'] ?? '电子资料兑换系统'); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($settings['site_keywords'] ?? '卡密,兑换,资料,教程'); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>">

    <link href="/static/css/user.css" rel="stylesheet">
    <link href="/static/css/query-modal.css" rel="stylesheet">
    <link href="/static/css/alert-modal.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Logo和副标题区域 -->
        <div class="brand-section">
            <?php if (!empty($settings['site_logo'])): ?>
            <div class="logo-container">
                <img src="<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>" class="site-logo">
            </div>
            <?php endif; ?>

            <?php if (!empty($settings['site_subtitle'])): ?>
            <div class="site-subtitle"><?php echo htmlspecialchars($settings['site_subtitle']); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="input-flex">
            <input type="text" id="cardNumber" placeholder="请输入卡密" />
            <button onclick="exchange()">立即兑换</button>
        </div>

        <button onclick="showQueryModal()" class="query-button">兑换记录查询</button>

        <div id="result" class="result-area"></div>
        
        <div class="note">
            <strong>说明：</strong> <?php echo htmlspecialchars($settings['page_footer_notice'] ?? '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'); ?>
        </div>
    </div>

    <!-- 悬浮联系客服模块 -->
    <?php if (!empty($settings['contact_qq']) || !empty($settings['contact_wechat']) || !empty($settings['contact_email']) || !empty($settings['contact_phone']) || !empty($settings['work_time'])): ?>
    <div class="floating-contact-container">
        <!-- PC端悬停卡片 -->
        <div class="contact-hover-card">
            <div class="contact-card-header">
                <i class="fas fa-headset me-2"></i>联系客服
            </div>
            <div class="contact-card-body">
                <?php if (!empty($settings['contact_wechat'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_wechat']); ?>', '微信')">
                    <i class="fab fa-weixin"></i>
                    <span><?php echo htmlspecialchars($settings['contact_wechat']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_qq'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_qq']); ?>', 'QQ')">
                    <i class="fab fa-qq"></i>
                    <span><?php echo htmlspecialchars($settings['contact_qq']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_email'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_email']); ?>', '邮箱')">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo htmlspecialchars($settings['contact_email']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_phone'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_phone']); ?>', '电话')">
                    <i class="fas fa-phone"></i>
                    <span><?php echo htmlspecialchars($settings['contact_phone']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['work_time'])): ?>
                <div class="contact-card-item">
                    <i class="fas fa-clock"></i>
                    <span><?php echo htmlspecialchars($settings['work_time']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 悬浮按钮 -->
        <div class="floating-contact-btn" onclick="handleContactClick()">
            <div class="contact-btn-bg"></div>
            <div class="contact-btn-pulse-1"></div>
            <div class="contact-btn-pulse-2"></div>
            <div class="contact-btn-pulse-3"></div>
            <div class="contact-btn-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="contact-btn-text">客服</div>
        </div>
    </div>

    <!-- 移动端弹窗 -->
    <div id="contactModal" class="contact-modal">
        <div class="contact-modal-content">
            <div class="contact-modal-header">
                <h4><i class="fas fa-headset me-2"></i>联系客服</h4>
                <span class="contact-close" onclick="closeContactModal()">&times;</span>
            </div>
            <div class="contact-modal-body">
                <?php if (!empty($settings['contact_wechat'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_wechat']); ?>', '微信')">
                    <i class="fab fa-weixin"></i>
                    <span>微信：<?php echo htmlspecialchars($settings['contact_wechat']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_qq'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_qq']); ?>', 'QQ')">
                    <i class="fab fa-qq"></i>
                    <span>QQ：<?php echo htmlspecialchars($settings['contact_qq']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_email'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_email']); ?>', '邮箱')">
                    <i class="fas fa-envelope"></i>
                    <span>邮箱：<?php echo htmlspecialchars($settings['contact_email']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_phone'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_phone']); ?>', '电话')">
                    <i class="fas fa-phone"></i>
                    <span>电话：<?php echo htmlspecialchars($settings['contact_phone']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['work_time'])): ?>
                <div class="contact-modal-item">
                    <i class="fas fa-clock"></i>
                    <span>工作时间：<?php echo htmlspecialchars($settings['work_time']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_address'])): ?>
                <div class="contact-modal-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>地址：<?php echo htmlspecialchars($settings['contact_address']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 页面底部信息 -->
    <footer class="site-footer">
        <div class="footer-content">
            <?php if (!empty($settings['copyright'])): ?>
            <div class="copyright">
                <?php echo htmlspecialchars($settings['copyright']); ?>
            </div>
            <?php else: ?>
            <div class="copyright">
                © 2024 红嘴鸥教育
            </div>
            <?php endif; ?>

            <?php if (!empty($settings['icp_number'])): ?>
            <div class="icp-info">
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
                    <?php echo htmlspecialchars($settings['icp_number']); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </footer>

    <!-- 滚动提示 -->
    <div id="scrollHint" class="scroll-hint">
        <span id="scrollText">向下滑动，查看剩余内容</span>
    </div>

    <!-- 查询弹窗 -->
    <div id="queryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>兑换记录查询</h3>
                <span class="close" onclick="closeQueryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>请输入要查询的卡密：</p>
                <input type="text" id="queryCardNumber" placeholder="请输入卡密" />
                <div class="modal-buttons">
                    <button onclick="queryExchangeRecord()" class="btn-primary">查询</button>
                    <button onclick="closeQueryModal()" class="btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 将前端设置传递给JavaScript
        window.frontendSettings = <?php echo json_encode($frontendSettings ?? [], JSON_UNESCAPED_UNICODE); ?>;

        // 滚动提示功能
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const scrollHint = document.getElementById('scrollHint');
            const scrollText = document.getElementById('scrollText');

            // 动态文字数组
            const scrollTexts = [
                '向下滑动，查看剩余内容',
                '还有更多内容等你发现',
                '继续滚动查看详情',
                '下方有更多精彩内容'
            ];
            let textIndex = 0;
            let textInterval;

            function startTextAnimation() {
                textInterval = setInterval(() => {
                    textIndex = (textIndex + 1) % scrollTexts.length;
                    scrollText.style.opacity = '0';
                    setTimeout(() => {
                        scrollText.textContent = scrollTexts[textIndex];
                        scrollText.style.opacity = '1';
                    }, 200);
                }, 3000);
            }

            function stopTextAnimation() {
                if (textInterval) {
                    clearInterval(textInterval);
                    textInterval = null;
                }
            }

            function checkScroll() {
                if (container.scrollHeight > container.clientHeight) {
                    const scrollTop = container.scrollTop;
                    const scrollHeight = container.scrollHeight;
                    const clientHeight = container.clientHeight;
                    const scrollPercentage = scrollTop / (scrollHeight - clientHeight);

                    // 当内容可滚动且未滚动到底部时显示提示
                    if (scrollPercentage < 0.9) {
                        scrollHint.classList.add('show');
                        if (!textInterval) {
                            startTextAnimation();
                        }
                    } else {
                        scrollHint.classList.remove('show');
                        stopTextAnimation();
                    }
                } else {
                    scrollHint.classList.remove('show');
                    stopTextAnimation();
                }
            }

            // 监听滚动事件
            container.addEventListener('scroll', checkScroll);

            // 监听内容变化
            const observer = new MutationObserver(function() {
                setTimeout(checkScroll, 100);
            });

            observer.observe(container, {
                childList: true,
                subtree: true,
                attributes: true
            });

            // 初始检查
            setTimeout(checkScroll, 500);

            // 添加文字过渡效果
            scrollText.style.transition = 'opacity 0.3s ease';
        });
    </script>
    <script src="/static/js/alert-modal.js"></script>
    <script src="/static/js/query-modal.js"></script>
    <script src="/static/js/contact-modal.js"></script>
    <script src="/static/js/exchange.js"></script>
</body>
</html>
