<?php
$page_title = '控制台';
$current_page = 'dashboard';

// 模拟统计数据
$stats = [
    'total_cards' => 1284,
    'used_cards' => 842,
    'unused_cards' => 442,
    'total_categories' => 16
];

$content = '
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card primary">
        <div class="stat-header">
            <h6 class="stat-title">卡密总数</h6>
            <div class="stat-icon primary">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 12% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card success">
        <div class="stat-header">
            <h6 class="stat-title">已使用卡密</h6>
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['used_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 8% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card warning">
        <div class="stat-header">
            <h6 class="stat-title">未使用卡密</h6>
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['unused_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 21% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card info">
        <div class="stat-header">
            <h6 class="stat-title">分类总数</h6>
            <div class="stat-icon info">
                <i class="fas fa-folder"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_categories']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 20% 较上月</span>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="7">7天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="30">30天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="90">90天</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密分布</h5>
            </div>
            <div style="height: 300px;">
                <canvas id="distributionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近使用的卡密 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">最近使用的卡密</h5>
        <a href="/admin/cards" class="btn btn-outline-primary btn-sm">查看全部</a>
    </div>
    
    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>卡密</th>
                    <th>分类</th>
                    <th>使用时间</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>XXXX-XXXX-XXXX-1234</code></td>
                    <td>网络安全</td>
                    <td>2023-06-15 09:42</td>
                    <td><span class="status-badge success">已使用</span></td>
                </tr>
                <tr>
                    <td><code>XXXX-XXXX-XXXX-5678</code></td>
                    <td>月度会员</td>
                    <td>2023-06-15 08:15</td>
                    <td><span class="status-badge success">已使用</span></td>
                </tr>
                <tr>
                    <td><code>XXXX-XXXX-XXXX-9012</code></td>
                    <td>编程开发</td>
                    <td>2023-06-14 16:30</td>
                    <td><span class="status-badge success">已使用</span></td>
                </tr>
                <tr>
                    <td><code>XXXX-XXXX-XXXX-3456</code></td>
                    <td>高级会员</td>
                    <td>2023-06-14 14:22</td>
                    <td><span class="status-badge success">已使用</span></td>
                </tr>
                <tr>
                    <td><code>XXXX-XXXX-XXXX-7890</code></td>
                    <td>网络安全</td>
                    <td>2023-06-14 11:05</td>
                    <td><span class="status-badge success">已使用</span></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 系统状态 -->
<div class="row">
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">系统状态</h5>
            </div>
            <div class="row g-3">
                <div class="col-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-server text-primary fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">服务器状态</div>
                            <div class="text-success">正常运行</div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-database text-info fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">数据库</div>
                            <div class="text-success">连接正常</div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-hdd text-warning fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">磁盘空间</div>
                            <div class="text-warning">78% 已使用</div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-memory text-success fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">内存使用</div>
                            <div class="text-success">45% 已使用</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">快捷操作</h5>
            </div>
            <div class="row g-3">
                <div class="col-6">
                    <a href="/admin/cards/create" class="btn btn-primary w-100 p-3">
                        <i class="fas fa-plus-circle fs-4 d-block mb-2"></i>
                        生成卡密
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/categories" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-folder-plus fs-4 d-block mb-2"></i>
                        分类管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/contents" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-file-plus fs-4 d-block mb-2"></i>
                        内容管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/settings" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-cog fs-4 d-block mb-2"></i>
                        系统设置
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 图表初始化
document.addEventListener("DOMContentLoaded", function() {
    // 使用趋势图表
    const usageCtx = document.getElementById("usageChart").getContext("2d");
    new Chart(usageCtx, {
        type: "line",
        data: {
            labels: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            datasets: [{
                label: "卡密使用量",
                data: [120, 150, 180, 200, 160, 190, 210, 180, 200, 220, 190, 250],
                borderColor: "rgb(79, 70, 229)",
                backgroundColor: "rgba(79, 70, 229, 0.1)",
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: "rgba(0, 0, 0, 0.1)"
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // 分布饼图
    const distributionCtx = document.getElementById("distributionChart").getContext("2d");
    new Chart(distributionCtx, {
        type: "doughnut",
        data: {
            labels: ["网络安全", "编程开发", "系统运维", "数据分析", "其他"],
            datasets: [{
                data: [30, 25, 20, 15, 10],
                backgroundColor: [
                    "rgb(79, 70, 229)",
                    "rgb(16, 185, 129)", 
                    "rgb(245, 158, 11)",
                    "rgb(239, 68, 68)",
                    "rgb(107, 114, 128)"
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: "bottom"
                }
            }
        }
    });
});
</script>
';

include 'layout.php';
?>
