<?php
$page_title = '控制台';
$current_page = 'dashboard';

// 使用从控制器传递的真实统计数据

$content = '
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card primary">
        <div class="stat-header">
            <h6 class="stat-title">卡密总数</h6>
            <div class="stat-icon primary">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 12% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card success">
        <div class="stat-header">
            <h6 class="stat-title">已使用卡密</h6>
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['used_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 8% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card warning">
        <div class="stat-header">
            <h6 class="stat-title">未使用卡密</h6>
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['unused_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 21% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card info">
        <div class="stat-header">
            <h6 class="stat-title">总兑换次数</h6>
            <div class="stat-icon info">
                <i class="fas fa-exchange-alt"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_exchanges']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>今日 ' . $stats['today_exchanges'] . ' 次</span>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="7">7天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="30">30天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="90">90天</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">快捷操作</h5>
            </div>
            <div class="row g-3 p-3">
                <div class="col-6">
                    <a href="/admin/cards/create" class="btn btn-primary w-100 p-3">
                        <i class="fas fa-plus-circle fs-4 d-block mb-2"></i>
                        生成卡密
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/categories" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-folder-plus fs-4 d-block mb-2"></i>
                        分类管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/contents" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-file-alt fs-4 d-block mb-2"></i>
                        内容管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/settings" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-cog fs-4 d-block mb-2"></i>
                        系统设置
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">最近活动</h5>
        <a href="/admin/cards" class="btn btn-outline-primary btn-sm">查看全部</a>
    </div>

    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>卡密</th>
                    <th>操作类型</th>
                    <th>操作时间</th>
                    <th>使用状态</th>
                    <th>启用状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>';

// 动态生成最近活动数据
if (!empty($stats['recent_activities'])) {
    foreach ($stats['recent_activities'] as $activity) {
        $actionTime = date('Y-m-d H:i', strtotime($activity['action_time']));

        // 根据使用状态设置标签 - 只有已使用和未使用两种状态
        $useStatusBadge = '';
        switch ($activity['use_status']) {
            case '未使用':
                $useStatusBadge = '<span class="status-badge warning">未使用</span>';
                break;
            case '已使用':
                $useStatusBadge = '<span class="status-badge success">已使用</span>';
                break;
            default:
                $useStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 根据启用状态设置标签 - 已禁用、已启用、已删除三种状态
        $enableStatusBadge = '';
        switch ($activity['enable_status']) {
            case '已启用':
                $enableStatusBadge = '<span class="status-badge success">已启用</span>';
                break;
            case '已禁用':
                $enableStatusBadge = '<span class="status-badge danger">已禁用</span>';
                break;
            case '已删除':
                $enableStatusBadge = '<span class="status-badge dark">已删除</span>';
                break;
            default:
                $enableStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 操作按钮
        $actionButton = '';
        if ($activity['card_id']) {
            $actionButton = '<button class="btn btn-outline-primary btn-sm" onclick="viewCardDetail(' . $activity['card_id'] . ')">
                                <i class="fas fa-eye"></i> 查看
                            </button>';
        } else {
            $actionButton = '<span class="text-muted">-</span>';
        }

        // 如果卡密已删除，添加删除样式
        $rowClass = $activity['is_deleted'] ? 'class="deleted-row"' : '';

        $content .= '
                <tr ' . $rowClass . '>
                    <td><code>' . htmlspecialchars($activity['card_number']) . '</code></td>
                    <td>' . htmlspecialchars($activity['action']) . '</td>
                    <td>' . $actionTime . '</td>
                    <td>' . $useStatusBadge . '</td>
                    <td>' . $enableStatusBadge . '</td>
                    <td>' . $actionButton . '</td>
                </tr>';
    }
} else {
    $content .= '
                <tr>
                    <td colspan="6" class="text-center text-muted">暂无活动记录</td>
                </tr>';
}

$content .= '
            </tbody>
        </table>
    </div>
</div>

<!-- 系统状态 -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">系统状态</h5>
            </div>
            <div class="row g-3">
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-server text-primary fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">服务器状态</div>
                            <div class="text-success">正常运行</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-database text-info fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">数据库</div>
                            <div class="text-success">连接正常</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-hdd text-warning fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">磁盘空间</div>
                            <div class="text-warning">78% 已使用</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-memory text-success fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">内存使用</div>
                            <div class="text-success">45% 已使用</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

</div>

<script>
// 图表初始化
document.addEventListener("DOMContentLoaded", function() {
    // 使用趋势图表
    const usageCtx = document.getElementById("usageChart").getContext("2d");
    let usageChart = new Chart(usageCtx, {
        type: "line",
        data: {
            labels: [],
            datasets: [{
                label: "兑换次数",
                data: [],
                borderColor: "rgb(79, 70, 229)",
                backgroundColor: "rgba(79, 70, 229, 0.1)",
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: "rgba(0, 0, 0, 0.1)"
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // 加载使用趋势数据
    function loadUsageTrend(period = 7) {
        fetch("/admin/getUsageTrend", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `period=${period}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                usageChart.data.labels = data.data.labels;
                usageChart.data.datasets[0].data = data.data.data;
                usageChart.update();
            } else {
                console.error("获取趋势数据失败:", data.msg);
            }
        })
        .catch(error => {
            console.error("网络错误:", error);
        });
    }

    // 绑定时间段按钮事件
    document.querySelectorAll("[data-period]").forEach(button => {
        button.addEventListener("click", function() {
            const period = parseInt(this.getAttribute("data-period"));

            // 更新按钮状态
            document.querySelectorAll("[data-period]").forEach(btn => {
                btn.classList.remove("active");
            });
            this.classList.add("active");

            // 加载对应时间段的数据
            loadUsageTrend(period);
        });
    });

    // 初始加载7天数据
    loadUsageTrend(7);

});

// 查看卡密详情
function viewCardDetail(cardId) {
    fetch("/admin/getCardDetail", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "card_id=" + cardId
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showCardDetailModal(data.data);
        } else {
            alert(data.msg || "获取详情失败");
        }
    })
    .catch(error => {
        console.error("获取详情错误:", error);
        alert("网络错误");
    });
}

// 显示卡密详情弹窗
function showCardDetailModal(cardData) {
    // 填充卡密号码
    document.getElementById(\'detail-card-number\').textContent = cardData.card_number;

    // 填充内容标题
    const contentList = document.getElementById(\'detail-content-list\');
    if (cardData.contents && cardData.contents.length > 0) {
        let contentHtml = \'\';
        cardData.contents.forEach((content, index) => {
            if (index > 0) contentHtml += \'<br>\';
            contentHtml += \'<span class="badge bg-primary me-1">\' + content.title + \'</span>\';
        });
        contentList.innerHTML = contentHtml;
    } else {
        contentList.innerHTML = \'<span class="text-muted">暂无关联内容</span>\';
    }

    // 填充使用状态
    document.getElementById(\'detail-use-status\').innerHTML = getStatusBadge(cardData.status, cardData.status_text);

    // 填充时间信息
    document.getElementById(\'detail-create-time\').textContent = cardData.create_time;
    document.getElementById(\'detail-used-time\').textContent = cardData.used_time ? cardData.used_time : \'-\';
    document.getElementById(\'detail-used-ip\').textContent = cardData.used_ip || \'-\';

    // 处理过期时间显示
    const expireTimeElement = document.getElementById(\'detail-expire-time\');
    if (!cardData.expire_time || cardData.expire_time === \'0000-00-00 00:00:00\' || cardData.expire_time === null) {
        expireTimeElement.innerHTML = \'<span class="text-success fw-bold">永久有效</span>\';
    } else {
        const expireDate = new Date(cardData.expire_time);
        const now = new Date();

        if (expireDate > now) {
            expireTimeElement.textContent = cardData.expire_time;
            expireTimeElement.className = \'text-muted\';
        } else {
            expireTimeElement.innerHTML = \'<span class="text-danger">\' + cardData.expire_time + \' (已过期)</span>\';
        }
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById(\'cardDetailsModal\'));
    modal.show();
}

// 生成状态标签
function getStatusBadge(status, statusText) {
    let badgeClass = "secondary";
    switch (status) {
        case 0: badgeClass = "danger"; break;
        case 1: badgeClass = "warning"; break;
        case 2: badgeClass = "success"; break;
    }
    return `<span class="badge bg-${badgeClass}">${statusText}</span>`;
}

// 格式化过期时间
function formatExpireTime(expireTime) {
    if (!expireTime || expireTime === "0000-00-00 00:00:00" || expireTime === null) {
        return \'<span class="text-success fw-bold">永久有效</span>\';
    }

    const expireDate = new Date(expireTime);
    const now = new Date();

    if (expireDate > now) {
        return expireTime;
    } else {
        return \'<span class="text-danger">\' + expireTime + \' (已过期)</span>\';
    }
}

// 生成内容部分
function generateContentsSection(contents) {
    if (!contents || contents.length === 0) {
        return "";
    }

    let html = `<div class="mt-3"><h6>关联内容:</h6><ul class="list-group">`;
    contents.forEach(content => {
        html += `<li class="list-group-item">${content.title}</li>`;
    });
    html += `</ul></div>`;
    return html;
}

// 生成兑换记录部分
function generateExchangeRecordsSection(records) {
    if (!records || records.length === 0) {
        return "";
    }

    let html = `<div class="mt-3"><h6>兑换记录:</h6><div class="table-responsive"><table class="table table-sm">`;
    html += `<thead><tr><th>兑换时间</th><th>IP地址</th><th>状态</th></tr></thead><tbody>`;
    records.forEach(record => {
        const statusBadge = record.status === 1 ?
            `<span class="badge bg-success">成功</span>` :
            `<span class="badge bg-danger">失败</span>`;
        html += `<tr><td>${record.exchange_time}</td><td>${record.exchange_ip || "-"}</td><td>${statusBadge}</td></tr>`;
    });
    html += `</tbody></table></div></div>`;
    return html;
}

// 复制卡密号码
function copyCardNumber() {
    const cardNumber = document.getElementById(\'detail-card-number\').textContent;
    if (navigator.clipboard) {
        navigator.clipboard.writeText(cardNumber).then(() => {
            alert(\'卡密已复制到剪贴板\');
        }).catch(() => {
            fallbackCopyTextToClipboard(cardNumber);
        });
    } else {
        fallbackCopyTextToClipboard(cardNumber);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand(\'copy\');
        alert(\'卡密已复制到剪贴板\');
    } catch (err) {
        alert(\'复制失败，请手动复制\');
    }

    document.body.removeChild(textArea);
}
</script>

<style>
/* 已删除卡密行样式 - 仅应用于表格行 */
tr.deleted-row {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    opacity: 0.8;
}

tr.deleted-row td {
    text-decoration: line-through;
    text-decoration-color: #dc3545;
    text-decoration-thickness: 1px;
}

/* 为已删除行添加轻微的边框提示 */
tr.deleted-row {
    border-left: 3px solid #dc3545 !important;
}
</style>

<!-- 卡密详情模态框 -->
<div class="modal fade" id="cardDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">卡密详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <!-- 卡密号码 -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>卡密号码：</strong>
                                </div>
                                <div class="col-9">
                                    <div class="d-flex align-items-center">
                                        <span id="detail-card-number" class="text-primary me-2"></span>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="copyCardNumber()" title="复制卡密">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内容标题 -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row">
                                <div class="col-3">
                                    <strong>内容标题：</strong>
                                </div>
                                <div class="col-9" id="detail-content-list">
                                    <!-- 关联内容列表将在这里显示 -->
                                </div>
                            </div>
                        </div>

                        <!-- 使用状态 -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>使用状态：</strong>
                                </div>
                                <div class="col-9">
                                    <span id="detail-use-status"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 生成时间 -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>生成时间：</strong>
                                </div>
                                <div class="col-9">
                                    <span id="detail-create-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用时间 -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>使用时间：</strong>
                                </div>
                                <div class="col-9">
                                    <span id="detail-used-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用者IP -->
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>使用者IP：</strong>
                                </div>
                                <div class="col-9">
                                    <span id="detail-used-ip" class="text-muted"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 过期时间 -->
                        <div class="mb-3">
                            <div class="row align-items-center">
                                <div class="col-3">
                                    <strong>过期时间：</strong>
                                </div>
                                <div class="col-9">
                                    <span id="detail-expire-time" class="text-muted"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
';

include 'layout.php';
?>
