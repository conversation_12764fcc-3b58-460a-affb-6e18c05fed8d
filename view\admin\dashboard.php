<?php
$page_title = '控制台';
$current_page = 'dashboard';

// 使用从控制器传递的真实统计数据

$content = '
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card primary">
        <div class="stat-header">
            <h6 class="stat-title">卡密总数</h6>
            <div class="stat-icon primary">
                <i class="fas fa-credit-card"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 12% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card success">
        <div class="stat-header">
            <h6 class="stat-title">已使用卡密</h6>
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['used_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 8% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card warning">
        <div class="stat-header">
            <h6 class="stat-title">未使用卡密</h6>
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['unused_cards']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>↑ 21% 较上月</span>
        </div>
    </div>
    
    <div class="stat-card info">
        <div class="stat-header">
            <h6 class="stat-title">总兑换次数</h6>
            <div class="stat-icon info">
                <i class="fas fa-exchange-alt"></i>
            </div>
        </div>
        <div class="stat-value">' . number_format($stats['total_exchanges']) . '</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>今日 ' . $stats['today_exchanges'] . ' 次</span>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-lg-8">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">卡密使用趋势</h5>
                <div class="chart-controls">
                    <button class="btn btn-outline-primary btn-sm active" data-period="7">7天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="30">30天</button>
                    <button class="btn btn-outline-primary btn-sm" data-period="90">90天</button>
                </div>
            </div>
            <div style="height: 300px;">
                <canvas id="usageChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">快捷操作</h5>
            </div>
            <div class="row g-3 p-3">
                <div class="col-6">
                    <a href="/admin/cards/create" class="btn btn-primary w-100 p-3">
                        <i class="fas fa-plus-circle fs-4 d-block mb-2"></i>
                        生成卡密
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/categories" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-folder-plus fs-4 d-block mb-2"></i>
                        分类管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/contents" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-file-alt fs-4 d-block mb-2"></i>
                        内容管理
                    </a>
                </div>
                <div class="col-6">
                    <a href="/admin/settings" class="btn btn-outline-primary w-100 p-3">
                        <i class="fas fa-cog fs-4 d-block mb-2"></i>
                        系统设置
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">最近活动</h5>
        <a href="/admin/cards" class="btn btn-outline-primary btn-sm">查看全部</a>
    </div>

    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th>卡密</th>
                    <th>操作类型</th>
                    <th>操作时间</th>
                    <th>使用状态</th>
                    <th>启用状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>';

// 动态生成最近活动数据
if (!empty($stats['recent_activities'])) {
    foreach ($stats['recent_activities'] as $activity) {
        $actionTime = date('Y-m-d H:i', strtotime($activity['action_time']));

        // 根据使用状态设置标签
        $useStatusBadge = '';
        switch ($activity['use_status']) {
            case '未使用':
                $useStatusBadge = '<span class="status-badge warning">未使用</span>';
                break;
            case '已使用':
                $useStatusBadge = '<span class="status-badge success">已使用</span>';
                break;
            case '已禁用':
                $useStatusBadge = '<span class="status-badge danger">已禁用</span>';
                break;
            default:
                $useStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 根据启用状态设置标签
        $enableStatusBadge = '';
        switch ($activity['enable_status']) {
            case '已启用':
                $enableStatusBadge = '<span class="status-badge success">已启用</span>';
                break;
            case '已禁用':
                $enableStatusBadge = '<span class="status-badge danger">已禁用</span>';
                break;
            default:
                $enableStatusBadge = '<span class="status-badge secondary">未知</span>';
        }

        // 操作按钮
        $actionButton = '';
        if ($activity['card_id']) {
            $actionButton = '<button class="btn btn-outline-primary btn-sm" onclick="viewCardDetail(' . $activity['card_id'] . ')">
                                <i class="fas fa-eye"></i> 查看
                            </button>';
        } else {
            $actionButton = '<span class="text-muted">-</span>';
        }

        // 如果卡密已删除，添加删除样式
        $rowClass = $activity['is_deleted'] ? 'class="deleted-row"' : '';

        $content .= '
                <tr ' . $rowClass . '>
                    <td><code>' . htmlspecialchars($activity['card_number']) . '</code></td>
                    <td>' . htmlspecialchars($activity['action']) . '</td>
                    <td>' . $actionTime . '</td>
                    <td>' . $useStatusBadge . '</td>
                    <td>' . $enableStatusBadge . '</td>
                    <td>' . $actionButton . '</td>
                </tr>';
    }
} else {
    $content .= '
                <tr>
                    <td colspan="6" class="text-center text-muted">暂无活动记录</td>
                </tr>';
}

$content .= '
            </tbody>
        </table>
    </div>
</div>

<!-- 系统状态 -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <div class="chart-header">
                <h5 class="chart-title">系统状态</h5>
            </div>
            <div class="row g-3">
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-server text-primary fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">服务器状态</div>
                            <div class="text-success">正常运行</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-database text-info fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">数据库</div>
                            <div class="text-success">连接正常</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-hdd text-warning fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">磁盘空间</div>
                            <div class="text-warning">78% 已使用</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <div class="me-3">
                            <i class="fas fa-memory text-success fs-4"></i>
                        </div>
                        <div>
                            <div class="fw-bold">内存使用</div>
                            <div class="text-success">45% 已使用</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

</div>

<script>
// 图表初始化
document.addEventListener("DOMContentLoaded", function() {
    // 使用趋势图表
    const usageCtx = document.getElementById("usageChart").getContext("2d");
    let usageChart = new Chart(usageCtx, {
        type: "line",
        data: {
            labels: [],
            datasets: [{
                label: "兑换次数",
                data: [],
                borderColor: "rgb(79, 70, 229)",
                backgroundColor: "rgba(79, 70, 229, 0.1)",
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: "rgba(0, 0, 0, 0.1)"
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // 加载使用趋势数据
    function loadUsageTrend(period = 7) {
        fetch("/admin/getUsageTrend", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `period=${period}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                usageChart.data.labels = data.data.labels;
                usageChart.data.datasets[0].data = data.data.data;
                usageChart.update();
            } else {
                console.error("获取趋势数据失败:", data.msg);
            }
        })
        .catch(error => {
            console.error("网络错误:", error);
        });
    }

    // 绑定时间段按钮事件
    document.querySelectorAll("[data-period]").forEach(button => {
        button.addEventListener("click", function() {
            const period = parseInt(this.getAttribute("data-period"));

            // 更新按钮状态
            document.querySelectorAll("[data-period]").forEach(btn => {
                btn.classList.remove("active");
            });
            this.classList.add("active");

            // 加载对应时间段的数据
            loadUsageTrend(period);
        });
    });

    // 初始加载7天数据
    loadUsageTrend(7);

});

// 查看卡密详情
function viewCardDetail(cardId) {
    fetch("/admin/getCardDetail", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `card_id=${cardId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showCardDetailModal(data.data);
        } else {
            AdminUtils.showMessage(data.msg || "获取详情失败", "error");
        }
    })
    .catch(error => {
        console.error("获取详情错误:", error);
        AdminUtils.showMessage("网络错误", "error");
    });
}

// 显示卡密详情弹窗
function showCardDetailModal(cardData) {
    const modalHtml =
        \'<div class="modal fade" id="cardDetailModal" tabindex="-1">\' +
            \'<div class="modal-dialog modal-lg">\' +
                \'<div class="modal-content">\' +
                    \'<div class="modal-header">\' +
                        \'<h5 class="modal-title">卡密详情</h5>\' +
                        \'<button type="button" class="btn-close" data-bs-dismiss="modal"></button>\' +
                    \'</div>\' +
                    \'<div class="modal-body">\' +
                        \'<div class="row">\' +
                            \'<div class="col-md-6">\' +
                                \'<table class="table table-borderless">\' +
                                    \'<tr><td><strong>卡密号码:</strong></td><td><code>\' + cardData.card_number + \'</code></td></tr>\' +
                                    \'<tr><td><strong>卡密类型:</strong></td><td>\' + cardData.card_type + \'</td></tr>\' +
                                    \'<tr><td><strong>批次ID:</strong></td><td>\' + (cardData.batch_id || \'-\') + \'</td></tr>\' +
                                    \'<tr><td><strong>分类:</strong></td><td>\' + cardData.category + \'</td></tr>\' +
                                    \'<tr><td><strong>面值:</strong></td><td>¥\' + cardData.value + \'</td></tr>\' +
                                    \'<tr><td><strong>有效天数:</strong></td><td>\' + cardData.valid_days + \'天</td></tr>\' +
                                \'</table>\' +
                            \'</div>\' +
                            \'<div class="col-md-6">\' +
                                \'<table class="table table-borderless">\' +
                                    \'<tr><td><strong>状态:</strong></td><td>\' + getStatusBadge(cardData.status, cardData.status_text) + \'</td></tr>\' +
                                    \'<tr><td><strong>使用时间:</strong></td><td>\' + (cardData.used_time || \'-\') + \'</td></tr>\' +
                                    \'<tr><td><strong>使用IP:</strong></td><td>\' + (cardData.used_ip || \'-\') + \'</td></tr>\' +
                                    \'<tr><td><strong>过期时间:</strong></td><td>\' + cardData.expire_time + \'</td></tr>\' +
                                    \'<tr><td><strong>创建时间:</strong></td><td>\' + cardData.create_time + \'</td></tr>\' +
                                    \'<tr><td><strong>更新时间:</strong></td><td>\' + cardData.update_time + \'</td></tr>\' +
                                \'</table>\' +
                            \'</div>\' +
                        \'</div>\' +
                        (cardData.remark ? \'<div class="mt-3"><strong>备注:</strong> \' + cardData.remark + \'</div>\' : \'\') +
                        generateContentsSection(cardData.contents) +
                        generateExchangeRecordsSection(cardData.exchange_records) +
                    \'</div>\' +
                    \'<div class="modal-footer">\' +
                        \'<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>\' +
                    \'</div>\' +
                \'</div>\' +
            \'</div>\' +
        \'</div>\';

    // 移除已存在的弹窗
    const existingModal = document.getElementById(\'cardDetailModal\');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新弹窗
    document.body.insertAdjacentHTML(\'beforeend\', modalHtml);

    // 显示弹窗
    const modal = new bootstrap.Modal(document.getElementById(\'cardDetailModal\'));
    modal.show();
}

// 生成状态标签
function getStatusBadge(status, statusText) {
    let badgeClass = \'secondary\';
    switch (status) {
        case 0: badgeClass = \'danger\'; break;
        case 1: badgeClass = \'warning\'; break;
        case 2: badgeClass = \'success\'; break;
    }
    return \'<span class="badge bg-\' + badgeClass + \'">\' + statusText + \'</span>\';
}

// 生成内容部分
function generateContentsSection(contents) {
    if (!contents || contents.length === 0) {
        return \'\';
    }

    let html = \'<div class="mt-3"><h6>关联内容:</h6><ul class="list-group">\';
    contents.forEach(content => {
        html += \'<li class="list-group-item">\' + content.title + \'</li>\';
    });
    html += \'</ul></div>\';
    return html;
}

// 生成兑换记录部分
function generateExchangeRecordsSection(records) {
    if (!records || records.length === 0) {
        return \'\';
    }

    let html = \'<div class="mt-3"><h6>兑换记录:</h6><div class="table-responsive"><table class="table table-sm">\';
    html += \'<thead><tr><th>兑换时间</th><th>IP地址</th><th>状态</th></tr></thead><tbody>\';
    records.forEach(record => {
        const statusBadge = record.status === 1 ?
            \'<span class="badge bg-success">成功</span>\' :
            \'<span class="badge bg-danger">失败</span>\';
        html += \'<tr><td>\' + record.exchange_time + \'</td><td>\' + (record.exchange_ip || \'-\') + \'</td><td>\' + statusBadge + \'</td></tr>\';
    });
    html += \'</tbody></table></div></div>\';
    return html;
}
</script>

<style>
.deleted-row {
    background-color: #f8d7da !important;
    text-decoration: line-through;
    color: #721c24 !important;
    opacity: 0.7;
}

.deleted-row td {
    position: relative;
}

.deleted-row::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #dc3545;
    z-index: 1;
}
</style>
';

include 'layout.php';
?>
