{extend name="admin/layout" /}

{block name="title"}控制台 - 卡密兑换管理系统{/block}

{block name="page_title"}控制台{/block}

{block name="css"}
<style>
    .stats-loading {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 8px;
        color: #6c757d;
    }
    
    .recent-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .recent-item:last-child {
        border-bottom: none;
    }
    
    .recent-info h6 {
        margin: 0;
        font-size: 0.9rem;
        color: #2c3e50;
    }
    
    .recent-info small {
        color: #6c757d;
    }
    
    .recent-time {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{/block}

{block name="content"}
<!-- 统计卡片 -->
<div class="stats-grid" id="statsGrid">
    <div class="stats-loading">
        <div class="loading"></div>
        正在加载统计数据...
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-md-8">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">卡密使用趋势</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-outline-primary">日</button>
                    <button class="btn btn-sm btn-outline-primary">周</button>
                    <button class="btn btn-sm btn-primary">月</button>
                </div>
            </div>
            <div id="trendChart" class="chart-placeholder">
                <div>
                    <div class="loading"></div>
                    <p>正在加载图表数据...</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">卡密分布</h3>
            </div>
            <div id="pieChart" class="chart-placeholder">
                <div>
                    <div class="loading"></div>
                    <p>正在加载图表数据...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近兑换记录 -->
<div class="table-container">
    <div class="chart-header">
        <h3 class="chart-title">最近使用记录</h3>
        <a href="/admin/cards" class="btn btn-sm btn-outline-primary">查看全部</a>
    </div>
    <div id="recentExchanges" class="p-3">
        <div class="stats-loading">
            <div class="loading"></div>
            正在加载记录...
        </div>
    </div>
</div>

<!-- 系统状态 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">系统状态</h3>
            </div>
            <div class="p-3">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-icon success mb-2">
                            <i class="bi bi-server"></i>
                        </div>
                        <h6>服务器状态</h6>
                        <small class="text-success">正常运行</small>
                    </div>
                    <div class="col-4">
                        <div class="stat-icon primary mb-2">
                            <i class="bi bi-database"></i>
                        </div>
                        <h6>数据库</h6>
                        <small class="text-success">连接正常</small>
                    </div>
                    <div class="col-4">
                        <div class="stat-icon warning mb-2">
                            <i class="bi bi-hdd"></i>
                        </div>
                        <h6>存储空间</h6>
                        <small class="text-warning">78% 已用</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">快捷操作</h3>
            </div>
            <div class="p-3">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="/admin/cards" class="btn btn-outline-primary w-100">
                            <i class="bi bi-plus-circle"></i>
                            添加卡密
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="/admin/categories" class="btn btn-outline-success w-100">
                            <i class="bi bi-folder-plus"></i>
                            添加分类
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="/admin/contents" class="btn btn-outline-info w-100">
                            <i class="bi bi-file-plus"></i>
                            添加内容
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="/admin/settings" class="btn btn-outline-warning w-100">
                            <i class="bi bi-gear"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="js"}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 页面加载完成后获取数据
    document.addEventListener('DOMContentLoaded', function() {
        loadDashboardData();
    });
    
    // 加载控制台数据
    function loadDashboardData() {
        Utils.ajax({
            url: '/admin/getStats',
            method: 'GET'
        }).then(response => {
            if (response.code === 1) {
                renderStats(response.data.overview);
                renderTrendChart(response.data.monthly_trend);
                renderPieChart(response.data.card_types);
                renderRecentExchanges(response.data.recent_exchanges);
            } else {
                Utils.showMessage('加载统计数据失败', 'error');
            }
        }).catch(error => {
            Utils.showMessage('网络错误，请稍后重试', 'error');
        });
    }
    
    // 渲染统计卡片
    function renderStats(stats) {
        const statsGrid = document.getElementById('statsGrid');
        
        const html = `
            <div class="stat-card primary">
                <div class="stat-header">
                    <h3 class="stat-title">总卡密数</h3>
                    <div class="stat-icon primary">
                        <i class="bi bi-credit-card"></i>
                    </div>
                </div>
                <h2 class="stat-value">${stats.total_cards}</h2>
                <p class="stat-change positive">↑ 12% 较上月</p>
            </div>
            
            <div class="stat-card success">
                <div class="stat-header">
                    <h3 class="stat-title">已使用</h3>
                    <div class="stat-icon success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
                <h2 class="stat-value">${stats.used_cards}</h2>
                <p class="stat-change positive">↑ 8% 较上月</p>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-header">
                    <h3 class="stat-title">未使用</h3>
                    <div class="stat-icon warning">
                        <i class="bi bi-clock"></i>
                    </div>
                </div>
                <h2 class="stat-value">${stats.unused_cards}</h2>
                <p class="stat-change negative">↓ 2% 较上月</p>
            </div>
            
            <div class="stat-card danger">
                <div class="stat-header">
                    <h3 class="stat-title">分类数量</h3>
                    <div class="stat-icon danger">
                        <i class="bi bi-folder"></i>
                    </div>
                </div>
                <h2 class="stat-value">${stats.total_categories}</h2>
                <p class="stat-change positive">↑ 20% 较上月</p>
            </div>
        `;
        
        statsGrid.innerHTML = html;
    }
    
    // 渲染趋势图表
    function renderTrendChart(data) {
        const ctx = document.getElementById('trendChart');
        ctx.innerHTML = '<canvas id="trendCanvas"></canvas>';
        
        const canvas = document.getElementById('trendCanvas');
        
        new Chart(canvas, {
            type: 'line',
            data: {
                labels: data.map(item => item.month),
                datasets: [{
                    label: '兑换数量',
                    data: data.map(item => item.count),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // 渲染饼图
    function renderPieChart(data) {
        const ctx = document.getElementById('pieChart');
        ctx.innerHTML = '<canvas id="pieCanvas"></canvas>';
        
        const canvas = document.getElementById('pieCanvas');
        
        const colors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6'];
        
        new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.card_type),
                datasets: [{
                    data: data.map(item => item.count),
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // 渲染最近兑换记录
    function renderRecentExchanges(data) {
        const container = document.getElementById('recentExchanges');
        
        if (data.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">暂无兑换记录</p>';
            return;
        }
        
        let html = '';
        data.forEach(record => {
            const statusClass = record.status == 1 ? 'success' : 'danger';
            const statusText = record.status == 1 ? '成功' : '失败';
            
            html += `
                <div class="recent-item">
                    <div class="recent-info">
                        <h6>${record.card_number}</h6>
                        <small>${record.card_type}</small>
                    </div>
                    <div class="text-end">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                        <div class="recent-time">${Utils.formatDate(record.exchange_time)}</div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
</script>
{/block}
