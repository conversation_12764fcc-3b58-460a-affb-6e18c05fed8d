<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{block name="title"}卡密兑换管理系统{/block}</title>
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/admin.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    {block name="css"}{/block}
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <a href="/admin/dashboard" class="sidebar-brand">
                    <i class="bi bi-shield-check"></i>
                    卡密兑换管理系统
                </a>
            </div>
            
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a href="/admin/dashboard" class="nav-link {eq name='$Request.action' value='dashboard'}active{/eq}">
                        <i class="bi bi-speedometer2"></i>
                        控制台
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/cards" class="nav-link {eq name='$Request.action' value='cards'}active{/eq}">
                        <i class="bi bi-credit-card"></i>
                        卡密管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/categories" class="nav-link {eq name='$Request.action' value='categories'}active{/eq}">
                        <i class="bi bi-folder"></i>
                        分类管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/contents" class="nav-link {eq name='$Request.action' value='contents'}active{/eq}">
                        <i class="bi bi-file-earmark-text"></i>
                        内容管理
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/settings" class="nav-link {eq name='$Request.action' value='settings'}active{/eq}">
                        <i class="bi bi-gear"></i>
                        系统设置
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-navbar">
                <h1 class="page-title">{block name="page_title"}控制台{/block}</h1>
                
                <div class="user-info">
                    <div class="dropdown">
                        <button class="dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {$admin.nickname|default=$admin.username}
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/profile"><i class="bi bi-person"></i> 个人资料</a></li>
                            <li><a class="dropdown-item" href="/admin/logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-wrapper">
                {block name="content"}{/block}
            </div>
        </main>
    </div>

    <script src="/static/js/common.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {block name="js"}{/block}
    
    <script>
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }
        
        // 点击外部关闭侧边栏
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const toggleBtn = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !toggleBtn?.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
        
        // 窗口大小改变时处理侧边栏
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>
