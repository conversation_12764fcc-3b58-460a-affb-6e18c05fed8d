/* 用户前端样式 */

/* 页面布局 */
.user-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 20px 80px 20px; /* 底部留出空间给footer */
    position: relative;
}

/* 品牌区域样式 */
.brand-section {
    text-align: center;
    margin-bottom: 2.5rem;
    padding: 0 20px;
}

/* Logo样式 */
.logo-container {
    margin-bottom: 1.2rem;
}

.site-logo {
    max-height: 70px;
    max-width: 200px;
    height: auto;
    width: auto;
    object-fit: contain;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.08));
    transition: transform 0.3s ease;
}

.site-logo:hover {
    transform: scale(1.02);
}

/* 主容器 */
.exchange-container {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    text-align: center;
}

/* 标题样式 */
.exchange-title {
    color: #333;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.exchange-subtitle {
    color: #666;
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 2rem;
    letter-spacing: 0.5px;
}

/* 输入框组 */
.input-group {
    display: flex;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.input-group .form-control {
    border: none;
    padding: 15px 20px;
    font-size: 1.1rem;
    border-radius: 0;
    flex: 1;
}

.input-group .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.input-group .btn {
    border: none;
    padding: 15px 25px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 0;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: #fff;
    transition: all 0.3s ease;
}

.input-group .btn:hover {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: translateY(-1px);
}

/* 查询按钮 */
.query-btn {
    background: linear-gradient(45deg, #00d2ff, #3a7bd5);
    color: #fff;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
}

.query-btn:hover {
    background: linear-gradient(45deg, #3a7bd5, #00d2ff);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 210, 255, 0.4);
}

/* 说明文字 */
.exchange-note {
    color: #888;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-top: 2rem;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

/* 结果显示 */
.exchange-result {
    margin-top: 1.5rem;
    padding: 15px;
    border-radius: 10px;
    font-weight: 500;
}

.exchange-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.exchange-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Simple页面样式 */
body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 20px 20px 80px 20px;
    box-sizing: border-box;
    overflow: hidden;
}

.container {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
    max-height: calc(100vh - 160px);
    overflow-y: auto;
    box-sizing: border-box;
    position: relative;
}

/* 隐藏滚动条但保持滚动功能 */
.container::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

.container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* 滚动提示样式 */
.scroll-hint {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(45deg, #ff6b6b, #ff8e53, #ff6b6b);
    background-size: 200% 200%;
    color: white;
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 600;
    z-index: 1001;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: pulse 2s infinite;
}

.scroll-hint.show {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

.scroll-hint::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b6b, #ff8e53, #4ecdc4, #45b7d1);
    background-size: 400% 400%;
    border-radius: 27px;
    z-index: -1;
    animation: gradientShift 3s ease infinite;
}

.scroll-hint::after {
    content: '↓';
    margin-left: 8px;
    font-size: 16px;
    animation: bounce 1.5s infinite;
    display: inline-block;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
    }
    50% {
        box-shadow: 0 6px 30px rgba(255, 107, 107, 0.6);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4px);
    }
    60% {
        transform: translateY(-2px);
    }
}

h1 {
    color: #333;
    margin-bottom: 10px;
}

.site-subtitle {
    color: #5a67d8;
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0;
    padding: 8px 18px;
    background: linear-gradient(135deg,
        rgba(90, 103, 216, 0.08),
        rgba(139, 92, 246, 0.08));
    border-radius: 20px;
    border: 1px solid rgba(90, 103, 216, 0.15);
    display: inline-block;
    letter-spacing: 0.3px;
    line-height: 1.5;
    box-shadow: 0 1px 3px rgba(90, 103, 216, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.site-subtitle:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(90, 103, 216, 0.2);
    background: linear-gradient(135deg,
        rgba(90, 103, 216, 0.12),
        rgba(139, 92, 246, 0.12));
    border-color: rgba(90, 103, 216, 0.25);
    color: #4c51bf;
}

/* 添加微妙的发光效果 */
.site-subtitle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    background: linear-gradient(135deg,
        rgba(90, 103, 216, 0.05),
        rgba(139, 92, 246, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.site-subtitle:hover::before {
    opacity: 1;
}

h2 {
    color: #666;
    margin-bottom: 30px;
}

.input-flex {
    display: flex;
    margin-bottom: 20px;
}

.input-flex input {
    width: 70%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px 0 0 10px;
    font-size: 16px;
}

.input-flex button {
    width: 25%;
    padding: 15px;
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 0 10px 10px 0;
    font-size: 16px;
    cursor: pointer;
}

.query-button {
    width: 200px;
    border-radius: 25px;
    background: #00d2ff;
    color: white;
    border: none;
    padding: 15px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
}

.result-area {
    margin-top: 20px;
}

.note {
    margin-top: 30px;
    padding: 18px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    color: #495057;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    border: 1px solid rgba(102, 126, 234, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    word-spacing: 0.5px;
    letter-spacing: 0.2px;
    overflow: hidden;
}

/* 顶部装饰条 */
.note::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

/* 图标装饰 */
.note::after {
    content: '📋';
    position: absolute;
    top: 18px;
    right: 16px;
    font-size: 16px;
    opacity: 0.6;
}

.note strong {
    color: #667eea;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 4px;
}

/* 顶部装饰条动画 */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* 内容显示样式 */
.download-btn {
    display: inline-block;
    padding: 10px 25px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white !important;
    text-decoration: none !important;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white !important;
}

.contact-info {
    color: #dc3545 !important;
    font-weight: bold !important;
    background: rgba(220, 53, 69, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* 链接容器样式 */
.link-container {
    display: inline-block;
    margin: 2px 0;
    vertical-align: baseline;
    max-width: 100%;
    word-break: break-all;
    word-wrap: break-word;
}

/* 内容链接样式 */
.content-link {
    color: #007bff !important;
    text-decoration: underline !important;
    font-weight: 500;
    line-height: 1.5;
    vertical-align: baseline;
    display: inline;
    word-break: break-all;
    word-wrap: break-word;
}

.content-link:hover {
    color: #0056b3 !important;
    text-decoration: none !important;
}

/* 复制按钮样式 */
.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 12px;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.2s ease;
    vertical-align: baseline;
    white-space: nowrap;
    line-height: 1;
    height: auto;
    display: inline-block;
    position: relative;
    top: 0;
    margin-left: 5px;
}

.copy-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

.copy-btn:active {
    transform: scale(0.95);
}

/* 复制成功提示样式 */
.copy-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #28a745;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 9999;
    font-weight: bold;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* 推广模块样式 */
.promotion-module {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.promotion-title {
    color: #dc3545;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}

.promotion-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: nowrap;
}

.promotion-btn {
    background: #6f42c1;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-block;
    flex: 1;
    text-align: center;
    max-width: 120px;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.promotion-btn:hover {
    background: #5a32a3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
    color: white;
    text-decoration: none;
}



.promotion-contact {
    color: #dc3545;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
}

.promotion-contact-value {
    color: #dc3545;
    font-weight: bold;
}

/* 兑换结果样式 */
.exchange-success {
    padding: 20px;
    margin-top: 15px;
    border-radius: 10px;
    background: #d4edda;
    color: #155724;
}

.exchange-error {
    padding: 15px;
    margin-top: 15px;
    border-radius: 10px;
    background: #f8d7da;
    color: #721c24;
}

.exchange-success h4 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.exchange-success p {
    margin: 8px 0;
    text-align: center;
}

.exchange-success h5 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.content-item {
    margin: 12px 0;
    padding: 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
}

.content-item h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-weight: bold;
    font-size: 1.1rem;
}

.content-detail {
    background: #fffbf0;
    padding: 15px;
    border-radius: 6px;
    margin: 10px 0;
    text-align: left;
    line-height: 1.8;
    border: 1px solid #f1c40f;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
}

.content-description {
    margin: 10px 0;
    padding: 10px;
    background: rgba(255,255,255,0.7);
    border-radius: 5px;
    font-style: italic;
    color: #666;
}

/* 查询结果样式 */
.query-success {
    padding: 20px;
    margin-top: 15px;
    border-radius: 10px;
    background: #d4edda;
    color: #155724;
}

.query-success h4 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.query-success h5 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.card-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    text-align: left;
}

.card-info h6 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.card-info p {
    margin: 5px 0;
}

.status-used {
    color: #dc3545;
    font-weight: bold;
}

.status-unused {
    color: #28a745;
    font-weight: bold;
}

.status-other {
    color: #ffc107;
    font-weight: bold;
}

/* 兑换内容标题样式 */
.exchange-content-title {
    color: #155724 !important;
    font-size: 1.1rem !important;
    font-weight: bold !important;
    margin: 15px 0 10px 0 !important;
    text-align: center !important;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .exchange-container {
        padding: 30px 20px;
        margin: 10px;
    }

    .exchange-title {
        font-size: 1.5rem;
    }

    .exchange-subtitle {
        font-size: 1.2rem;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group .form-control,
    .input-group .btn {
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .input-group .btn {
        margin-bottom: 0;
    }

    .brand-section {
        margin-bottom: 2rem;
        padding: 0 15px;
    }

    .site-logo {
        max-height: 55px;
        max-width: 160px;
    }

    .site-subtitle {
        font-size: 0.85rem;
        padding: 6px 14px;
        margin: 0 10px;
        letter-spacing: 0.2px;
    }

    /* 移动端输入框和按钮样式 */
    .input-flex {
        flex-direction: column;
        gap: 10px;
    }

    .input-flex input {
        width: 100%;
        border-radius: 10px;
        font-size: 16px;
        padding: 15px;
        box-sizing: border-box;
    }

    .input-flex button {
        width: 100%;
        border-radius: 10px;
        font-size: 16px;
        padding: 15px;
        white-space: nowrap;
        min-width: auto;
        box-sizing: border-box;
    }

    /* 移动端说明部分优化 */
    .note {
        padding: 14px 12px;
        font-size: 13px;
        margin-top: 20px;
        text-align: justify;
        line-height: 1.7;
        word-spacing: 0.3px;
        letter-spacing: 0.1px;
    }

    .note::after {
        top: 14px;
        right: 12px;
        font-size: 14px;
    }

    .note strong {
        display: block;
        margin-bottom: 6px;
    }

    /* 移动端容器优化 */
    .container {
        padding: 20px 12px;
        margin: 8px;
    }

    /* 移动端body内边距优化 */
    body {
        padding: 15px 10px 80px 10px;
    }

    /* 移动端内容显示优化 */
    .content-item {
        margin: 8px 0;
        padding: 8px;
        font-size: 13px;
    }

    .content-item h4 {
        font-size: 1rem;
        margin: 0 0 8px 0;
    }

    .exchange-content-title {
        font-size: 1rem !important;
        margin: 10px 0 8px 0 !important;
    }

    /* 移动端滚动提示位置调整 */
    .scroll-hint {
        bottom: 90px;
        font-size: 12px;
        padding: 8px 14px;
    }

    /* 移动端推广按钮优化 */
    .promotion-buttons {
        gap: 6px;
        padding: 0 5px;
    }

    .promotion-btn {
        font-size: 12px;
        padding: 6px 8px;
        max-width: 100px;
    }
}

/* 底部信息样式 */
.site-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 15px 20px;
    text-align: center;
    z-index: 1000;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
}

.copyright,
.icp-info {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.85rem;
    line-height: 1.4;
}

.icp-info a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.icp-info a:hover {
    color: #fff;
    text-decoration: underline;
}

/* 响应式底部信息 */
@media (min-width: 768px) {
    .footer-content {
        flex-direction: row;
        justify-content: center;
        gap: 20px;
    }

    .copyright::after {
        content: '|';
        margin-left: 20px;
        opacity: 0.6;
    }
}
