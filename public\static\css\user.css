/* 用户前端样式 */

/* 页面布局 */
.user-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 20px 80px 20px; /* 底部留出空间给footer */
    position: relative;
}

/* Logo样式 */
.logo-container {
    text-align: center;
    margin-bottom: 1rem;
}

.site-logo {
    max-height: 80px;
    max-width: 200px;
    height: auto;
    width: auto;
    object-fit: contain;
}

/* 主容器 */
.exchange-container {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    text-align: center;
}

/* 标题样式 */
.exchange-title {
    color: #333;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.exchange-subtitle {
    color: #666;
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 2rem;
    letter-spacing: 0.5px;
}

/* 输入框组 */
.input-group {
    display: flex;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.input-group .form-control {
    border: none;
    padding: 15px 20px;
    font-size: 1.1rem;
    border-radius: 0;
    flex: 1;
}

.input-group .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.input-group .btn {
    border: none;
    padding: 15px 25px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 0;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: #fff;
    transition: all 0.3s ease;
}

.input-group .btn:hover {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: translateY(-1px);
}

/* 查询按钮 */
.query-btn {
    background: linear-gradient(45deg, #00d2ff, #3a7bd5);
    color: #fff;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
}

.query-btn:hover {
    background: linear-gradient(45deg, #3a7bd5, #00d2ff);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 210, 255, 0.4);
}

/* 说明文字 */
.exchange-note {
    color: #888;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-top: 2rem;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

/* 结果显示 */
.exchange-result {
    margin-top: 1.5rem;
    padding: 15px;
    border-radius: 10px;
    font-weight: 500;
}

.exchange-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.exchange-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Simple页面样式 */
body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 20px 20px 80px 20px; /* 底部留出空间给footer */
}

.container {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
}

h1 {
    color: #333;
    margin-bottom: 10px;
}

.site-subtitle {
    color: #888;
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 15px;
    font-style: italic;
}

h2 {
    color: #666;
    margin-bottom: 30px;
}

.input-flex {
    display: flex;
    margin-bottom: 20px;
}

.input-flex input {
    width: 70%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px 0 0 10px;
    font-size: 16px;
}

.input-flex button {
    width: 25%;
    padding: 15px;
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 0 10px 10px 0;
    font-size: 16px;
    cursor: pointer;
}

.query-button {
    width: 200px;
    border-radius: 25px;
    background: #00d2ff;
    color: white;
    border: none;
    padding: 15px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
}

.result-area {
    margin-top: 20px;
}

.note {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #666;
    font-size: 14px;
}

/* 内容显示样式 */
.download-btn {
    display: inline-block;
    padding: 10px 25px;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white !important;
    text-decoration: none !important;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white !important;
}

.contact-info {
    color: #dc3545 !important;
    font-weight: bold !important;
    background: rgba(220, 53, 69, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* 链接容器样式 */
.link-container {
    display: inline-block;
    margin: 2px 0;
    vertical-align: baseline;
    max-width: 100%;
    word-break: break-all;
    word-wrap: break-word;
}

/* 内容链接样式 */
.content-link {
    color: #007bff !important;
    text-decoration: underline !important;
    font-weight: 500;
    line-height: 1.5;
    vertical-align: baseline;
    display: inline;
    word-break: break-all;
    word-wrap: break-word;
}

.content-link:hover {
    color: #0056b3 !important;
    text-decoration: none !important;
}

/* 复制按钮样式 */
.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 12px;
    font-family: inherit;
    font-weight: 500;
    transition: all 0.2s ease;
    vertical-align: baseline;
    white-space: nowrap;
    line-height: 1;
    height: auto;
    display: inline-block;
    position: relative;
    top: 0;
    margin-left: 5px;
}

.copy-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

.copy-btn:active {
    transform: scale(0.95);
}

/* 复制成功提示样式 */
.copy-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #28a745;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 9999;
    font-weight: bold;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* 推广模块样式 */
.promotion-module {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.promotion-title {
    color: #dc3545;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}

.promotion-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.promotion-btn {
    background: #6f42c1;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-block;
}

.promotion-btn:hover {
    background: #5a32a3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
    color: white;
    text-decoration: none;
}



.promotion-contact {
    color: #dc3545;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
}

.promotion-contact-value {
    color: #dc3545;
    font-weight: bold;
}

/* 兑换结果样式 */
.exchange-success {
    padding: 20px;
    margin-top: 15px;
    border-radius: 10px;
    background: #d4edda;
    color: #155724;
}

.exchange-error {
    padding: 15px;
    margin-top: 15px;
    border-radius: 10px;
    background: #f8d7da;
    color: #721c24;
}

.exchange-success h4 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.exchange-success p {
    margin: 8px 0;
    text-align: center;
}

.exchange-success h5 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.content-item {
    margin: 15px 0;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    text-align: center;
}

.content-item h4 {
    margin: 0 0 15px 0;
    color: #856404;
    font-weight: bold;
}

.content-detail {
    background: #fffbf0;
    padding: 15px;
    border-radius: 6px;
    margin: 10px 0;
    text-align: left;
    line-height: 1.8;
    border: 1px solid #f1c40f;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
}

.content-description {
    margin: 10px 0;
    padding: 10px;
    background: rgba(255,255,255,0.7);
    border-radius: 5px;
    font-style: italic;
    color: #666;
}

/* 查询结果样式 */
.query-success {
    padding: 20px;
    margin-top: 15px;
    border-radius: 10px;
    background: #d4edda;
    color: #155724;
}

.query-success h4 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.query-success h5 {
    margin: 0 0 15px 0;
    color: #155724;
    text-align: center;
}

.card-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    text-align: left;
}

.card-info h6 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.card-info p {
    margin: 5px 0;
}

.status-used {
    color: #dc3545;
    font-weight: bold;
}

.status-unused {
    color: #28a745;
    font-weight: bold;
}

.status-other {
    color: #ffc107;
    font-weight: bold;
}

/* 兑换内容标题样式 */
.exchange-content-title {
    color: #155724 !important;
    font-size: 1.3rem !important;
    font-weight: bold !important;
    margin: 20px 0 15px 0 !important;
    text-align: center !important;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .exchange-container {
        padding: 30px 20px;
        margin: 10px;
    }

    .exchange-title {
        font-size: 1.5rem;
    }

    .exchange-subtitle {
        font-size: 1.2rem;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group .form-control,
    .input-group .btn {
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .input-group .btn {
        margin-bottom: 0;
    }

    .site-logo {
        max-height: 60px;
        max-width: 150px;
    }
}

/* 底部信息样式 */
.site-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 15px 20px;
    text-align: center;
    z-index: 1000;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
}

.copyright,
.icp-info {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.85rem;
    line-height: 1.4;
}

.icp-info a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.icp-info a:hover {
    color: #fff;
    text-decoration: underline;
}

/* 响应式底部信息 */
@media (min-width: 768px) {
    .footer-content {
        flex-direction: row;
        justify-content: center;
        gap: 20px;
    }

    .copyright::after {
        content: '|';
        margin-left: 20px;
        opacity: 0.6;
    }
}
