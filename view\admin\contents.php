<?php
$page_title = '内容管理';
$current_page = 'contents';
$extra_js = ['/static/js/contents.js'];

// 开始输出缓冲
ob_start();
?>
<!-- 页面标题 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="page-title">内容管理</h1>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="addContent()">
                <i class="fas fa-plus me-2"></i>添加内容
            </button>
        </div>
    </div>
</div>

<!-- 内容概况 -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['total']; ?></h3>
                        <p class="stats-label">总内容数</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['enabled']; ?></h3>
                        <p class="stats-label">启用内容</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['disabled']; ?></h3>
                        <p class="stats-label">禁用内容</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form class="row g-3 align-items-end" method="GET">
            <input type="hidden" name="page_size" value="<?php echo isset($search_params['page_size']) ? $search_params['page_size'] : 10; ?>">
            <div class="col-md-3">
                <div class="row align-items-center g-1">
                    <div class="col-auto">
                        <label class="form-label mb-0 small">分类筛选</label>
                    </div>
                    <div class="col">
                        <select class="form-select form-select-sm" name="category_id">
                            <option value="">全部分类</option>
                            <?php
                            // 显示所有分类用于筛选，但标记哪些可以添加内容
                            $categories = \app\model\Category::getAllForAdmin();
                            foreach ($categories as $category) {
                                if ($category['status'] == 1) { // 只显示启用的分类
                                    $indent = str_repeat('　', $category['level'] - 1);
                                    if ($category['level'] > 1) {
                                        $indent .= '├─ ';
                                    }
                                    $selected = (isset($search_params['category_id']) && $search_params['category_id'] == $category['id']) ? 'selected' : '';
                                    echo '<option value="' . $category['id'] . '" ' . $selected . '>' . $indent . htmlspecialchars($category['name']) . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-2">
                <div class="row align-items-center g-1">
                    <div class="col-auto">
                        <label class="form-label mb-0 small">状态</label>
                    </div>
                    <div class="col">
                        <select class="form-select form-select-sm" name="status">
                            <option value="">全部</option>
                            <option value="1" <?php echo (isset($search_params['status']) && $search_params['status'] === '1') ? 'selected' : ''; ?>>启用</option>
                            <option value="0" <?php echo (isset($search_params['status']) && $search_params['status'] === '0') ? 'selected' : ''; ?>>禁用</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="row align-items-center g-1">
                    <div class="col-auto">
                        <label class="form-label mb-0 small">搜索</label>
                    </div>
                    <div class="col">
                        <input type="text" class="form-control form-control-sm" name="search" placeholder="输入标题搜索" value="<?php echo isset($search_params['search']) ? htmlspecialchars($search_params['search']) : ''; ?>">
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="?" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-redo me-1"></i>重置
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 内容列表 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">内容列表</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-danger btn-sm" onclick="forceBatchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="exportContents()">
                <i class="fas fa-download me-1"></i>导出
            </button>
        </div>
    </div>
    
    <div class="table-container">
        <table class="table" id="contentsTable">
            <thead>
                <tr>
                    <th width="5%" class="text-center">
                        <div class="form-check d-flex justify-content-center">
                            <input class="form-check-input" type="checkbox" id="selectAll">
                        </div>
                    </th>
                    <th width="25%" class="text-center">标题</th>
                    <th width="25%" class="text-center">分类路径</th>
                    <th width="10%" class="text-center">状态</th>
                    <th width="10%" class="text-center">排序</th>
                    <th width="15%" class="text-center">创建时间</th>
                    <th width="15%" class="text-center">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (isset($contents) && !empty($contents)): ?>
                    <?php foreach ($contents as $content): ?>
                    <tr>
                        <td class="text-center">
                            <div class="form-check d-flex justify-content-center">
                                <input class="form-check-input row-select" type="checkbox" value="<?php echo $content['id']; ?>">
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="fw-bold"><?php echo htmlspecialchars($content['title']); ?></span>
                        </td>
                        <td class="text-center">
                            <?php if (isset($content['category_path'])): ?>
                                <small class="text-muted"><?php echo htmlspecialchars($content['category_path']); ?></small>
                            <?php elseif (isset($content['category'])): ?>
                                <small class="text-muted"><?php echo htmlspecialchars($content['category']['name']); ?></small>
                            <?php else: ?>
                                <span class="text-muted">未分类</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <div class="form-check form-switch d-flex justify-content-center">
                                <input class="form-check-input content-status-switch" type="checkbox"
                                       <?php echo ($content['status'] == 1) ? 'checked' : ''; ?>
                                       data-id="<?php echo $content['id']; ?>"
                                       onchange="toggleContentStatus(<?php echo $content['id']; ?>, this)">
                            </div>
                        </td>
                        <td class="text-center">
                            <input type="number" class="form-control sort-input"
                                   value="<?php echo $content['sort_order']; ?>"
                                   data-id="<?php echo $content['id']; ?>"
                                   data-original="<?php echo $content['sort_order']; ?>"
                                   onchange="updateContentSort(<?php echo $content['id']; ?>, this.value)"
                                   onblur="updateContentSort(<?php echo $content['id']; ?>, this.value)"
                                   min="0"
                                   title="直接修改数字即可更新排序">
                        </td>
                        <td class="text-center">
                            <small class="text-muted"><?php echo date('Y-m-d', strtotime($content['create_time'])); ?></small>
                        </td>
                        <td class="text-center">
                            <div class="category-actions">
                                <button class="category-action-btn edit-btn" onclick="editContent(<?php echo $content['id']; ?>)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="category-action-btn" onclick="viewContent(<?php echo $content['id']; ?>)" title="查看" style="background: var(--info-light); color: var(--info-color);">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="category-action-btn delete-btn" onclick="deleteContent(<?php echo $content['id']; ?>)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>暂无内容数据</p>
                        </td>
                    </tr>
                <?php endif; ?>


            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container mt-3">
        <div class="pagination-info">
            <?php if (isset($contents) && method_exists($contents, 'render')): ?>
                <span class="text-muted">
                    共 <?php echo $contents->total(); ?> 条记录，
                    当前第 <?php echo $contents->currentPage(); ?> 页，
                    共 <?php echo $contents->lastPage(); ?> 页
                </span>
                <div class="page-size-selector">
                    <span class="page-size-label">每页显示</span>
                    <select class="page-size-select" onchange="changePageSize(this.value)">
                        <option value="10" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 10) ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 20) ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 50) ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo (isset($search_params['page_size']) && $search_params['page_size'] == 100) ? 'selected' : ''; ?>>100</option>
                    </select>
                    <span class="page-size-unit">条</span>
                </div>
            <?php else: ?>
                <span class="text-muted">暂无数据</span>
                <div class="page-size-selector">
                    <span class="page-size-label">每页显示</span>
                    <select class="page-size-select" onchange="changePageSize(this.value)">
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="page-size-unit">条</span>
                </div>
            <?php endif; ?>
        </div>

        <div class="pagination-nav">
            <?php if (isset($pagination_html)): ?>
                <?php echo $pagination_html; ?>
            <?php else: ?>
                <!-- 默认分页 -->
                <nav aria-label="分页导航">
                    <ul class="custom-pagination">
                        <li class="custom-page-item disabled">
                            <span class="custom-page-link disabled">上一页</span>
                        </li>
                        <li class="custom-page-item active">
                            <span class="custom-page-link active">1</span>
                        </li>
                        <li class="custom-page-item disabled">
                            <span class="custom-page-link disabled">下一页</span>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 内容模态框 -->
<div class="modal fade" id="contentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加内容</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="contentForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="row align-items-center">
                            <div class="col-3">
                                <label class="form-label mb-0">内容标题</label>
                            </div>
                            <div class="col-9">
                                <input type="text" class="form-control" name="title" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="row align-items-center">
                            <div class="col-3">
                                <label class="form-label mb-0">所属分类</label>
                            </div>
                            <div class="col-9">
                                <select class="form-select" name="category_id" required id="categorySelect">
                                    <option value="">请选择分类</option>
                                    <?php
                                    // 显示完整的分类结构
                                    $categories = \app\model\Category::getAllForAdmin();
                                    foreach ($categories as $category) {
                                        if ($category['status'] == 1) { // 只显示启用的分类
                                            $indent = str_repeat('　', $category['level'] - 1);
                                            if ($category['level'] > 1) {
                                                $indent .= '├─ ';
                                            }

                                            // 检查是否为叶子节点
                                            $categoryModel = \app\model\Category::find($category['id']);
                                            $isLeaf = $categoryModel ? $categoryModel->isLeafNode() : false;
                                            $disabled = $isLeaf ? '' : 'disabled';
                                            $style = $isLeaf ? '' : 'color: #999;';

                                            echo '<option value="' . $category['id'] . '" ' . $disabled . ' style="' . $style . '" data-is-leaf="' . ($isLeaf ? 'true' : 'false') . '">' . $indent . htmlspecialchars($category['name']) . ($isLeaf ? '' : ' (不可选择)') . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        只能在最低级分类中添加内容，灰色分类不可选择
                                    </small>
                                </div>
                                <div id="categoryPathDisplay" class="mt-2" style="display: none;">
                                    <small class="text-primary">
                                        <i class="fas fa-sitemap me-1"></i>
                                        分类路径：<span id="categoryPath"></span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="row">
                            <div class="col-3">
                                <label class="form-label mb-0">详细内容</label>
                            </div>
                            <div class="col-9">
                                <textarea class="form-control" name="content" rows="6" placeholder="输入详细的内容说明..." required></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="row align-items-center">
                            <div class="col-3">
                                <label class="form-label mb-0">排序权重</label>
                            </div>
                            <div class="col-9">
                                <input type="number" class="form-control" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="row align-items-center">
                            <div class="col-3">
                                <label class="form-label mb-0">状态设置</label>
                            </div>
                            <div class="col-9">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="status" value="1" checked>
                                    <label class="form-check-label">启用内容</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存内容</button>
                </div>
            </form>
        </div>
    </div>
</div>



<script>
// 全选功能
document.getElementById("selectAll").addEventListener("change", function() {
    const checkboxes = document.querySelectorAll(".row-select");
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 切换内容状态（开关使用）
function toggleContentStatus(id, element) {
    const status = element.checked ? 1 : 0;

    // 显示加载状态
    element.disabled = true;

    fetch('/admin/toggleContentStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${encodeURIComponent(id)}&status=${encodeURIComponent(status)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 成功时显示简短提示
            if (window.AdminUtils) {
                AdminUtils.showMessage(status ? "已启用" : "已禁用", "success", 2000);
            }
            // 无感刷新内容列表
            refreshContentList();
        } else {
            // 如果失败，恢复开关状态
            element.checked = !element.checked;
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "操作失败", "error");
            } else {
                alert(data.msg || "操作失败");
            }
        }
    })
    .catch(error => {
        // 如果出错，恢复开关状态
        element.checked = !element.checked;
        console.error('状态更新错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误", "error");
        } else {
            alert("网络错误");
        }
    })
    .finally(() => {
        // 恢复开关状态
        element.disabled = false;
    });
}



// 添加/编辑内容 - 防止重复绑定和重复提示
const contentForm = document.getElementById("contentForm");
let isSubmitting = false; // 防止重复提交

if (contentForm && !contentForm.hasAttribute('data-listener-added')) {
    contentForm.setAttribute('data-listener-added', 'true');
    contentForm.addEventListener("submit", function(e) {
        e.preventDefault();

        // 防止重复提交
        if (isSubmitting) {
            return;
        }

        isSubmitting = true;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.textContent = '保存中...';

        const formData = new FormData(this);
        const isEdit = formData.get('id') && formData.get('id') !== '';

        fetch("/admin/saveContent", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                AdminUtils.showMessage(data.msg, "success");
                bootstrap.Modal.getInstance(document.getElementById("contentModal")).hide();

                // 无感刷新内容列表
                refreshContentList();
            } else {
                AdminUtils.showMessage(data.msg, "error");
            }
        })
        .catch(error => {
            console.error("保存内容错误:", error);
            AdminUtils.showMessage("保存失败：网络错误", "error");
        })
        .finally(() => {
            // 恢复按钮状态
            isSubmitting = false;
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });
}

// 添加内容
function addContent() {
    // 重置表单
    document.getElementById('contentForm').reset();

    // 设置模态框标题
    document.querySelector('#contentModal .modal-title').textContent = '添加内容';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('contentModal'));
    modal.show();
}

// 编辑内容
function editContent(id) {
    fetch(`/admin/getContent?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const content = data.data;

            // 填充表单数据
            document.getElementById('contentForm').reset();
            document.querySelector('input[name="id"]').value = content.id;
            document.querySelector('input[name="title"]').value = content.title;
            document.querySelector('select[name="category_id"]').value = content.category_id || '';
            document.querySelector('textarea[name="description"]').value = content.description || '';
            document.querySelector('input[name="status"]').checked = content.status == 1;

            // 设置模态框标题
            document.querySelector('#contentModal .modal-title').textContent = '编辑内容';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('contentModal'));
            modal.show();
        } else {
            AdminUtils.showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error("获取内容错误:", error);
        AdminUtils.showMessage("获取内容失败：网络错误", "error");
    });
}

// 预览内容
function previewContent(id) {
    console.log("预览内容:", id);
    AdminUtils.showMessage("预览功能开发中...", "info");
}

// 删除内容
function deleteContent(id) {
    AdminUtils.confirm("确定要删除这个内容吗？删除后无法恢复！", function() {
        performDeleteContent(id);
    });
}

// 执行删除内容
function performDeleteContent(id) {
    fetch("/admin/deleteContent", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `id=${encodeURIComponent(id)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            AdminUtils.showMessage(data.msg, "success");
            // 无感刷新内容列表
            refreshContentList();
        } else {
            AdminUtils.showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error("删除内容错误:", error);
        AdminUtils.showMessage("删除失败：网络错误", "error");
    });
}



// 批量删除（包括关联的卡密）
function forceBatchDelete() {
    const selected = document.querySelectorAll(".row-select:checked");
    if (selected.length === 0) {
        AdminUtils.showMessage("请选择要删除的内容", "warning");
        return;
    }

    // 获取选中的ID数组
    const ids = Array.from(selected).map(checkbox => checkbox.value);

    AdminUtils.confirm(`确定要删除选中的 ${ids.length} 个内容吗？\n\n⚠️ 注意：如果内容关联了卡密，将同时删除关联的卡密，操作无法恢复！`, function() {
        performForceBatchDelete(ids);
    });
}

// 执行强制批量删除
function performForceBatchDelete(ids) {
    const formData = new FormData();
    ids.forEach((id, index) => {
        formData.append(`ids[${index}]`, id);
    });

    fetch("/admin/batchDeleteContents", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            AdminUtils.showMessage(data.msg, "success");
            // 无感刷新内容列表
            refreshContentList();
        } else {
            AdminUtils.showMessage(data.msg, "error");
            // 显示详细错误信息
            if (data.data && data.data.errors && data.data.errors.length > 0) {
                setTimeout(() => {
                    AdminUtils.showMessage("详细信息：\n" + data.data.errors.join('\n'), "warning", 8000);
                }, 1000);
            }
        }
    })
    .catch(error => {
        console.error("强制删除错误:", error);
        AdminUtils.showMessage("强制删除失败：网络错误", "error");
    });
}

// 防抖动变量
let refreshTimeout = null;

// 无感刷新内容列表
function refreshContentList() {
    // 防抖动处理
    if (refreshTimeout) {
        clearTimeout(refreshTimeout);
    }

    refreshTimeout = setTimeout(() => {
        performRefresh();
    }, 100);
}

// 执行刷新操作
function performRefresh() {
    // 保存当前滚动位置
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // 获取当前页面的搜索参数
    const urlParams = new URLSearchParams(window.location.search);
    const currentParams = {
        category_id: urlParams.get('category_id') || '',
        status: urlParams.get('status') || '',
        search: urlParams.get('search') || '',
        page: urlParams.get('page') || '1',
        page_size: urlParams.get('page_size') || '10'
    };

    // 构建请求URL
    const queryString = Object.keys(currentParams)
        .filter(key => currentParams[key] !== '')
        .map(key => `${key}=${encodeURIComponent(currentParams[key])}`)
        .join('&');

    const url = `/admin/getContentList${queryString ? '?' + queryString : ''}`;

    fetch(url)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 更新内容列表
            updateContentTable(data.data.contents);
            // 更新分页
            updatePagination(data.data.pagination_html);
            // 更新统计信息
            if (data.data.stats) {
                updateStats(data.data.stats);
            }

            // 恢复滚动位置
            setTimeout(() => {
                window.scrollTo({
                    top: scrollPosition,
                    behavior: 'instant'
                });
            }, 50);
        } else {
            console.error("刷新列表失败:", data.msg);
        }
    })
    .catch(error => {
        console.error("刷新列表错误:", error);
    });
}

// 更新内容表格
function updateContentTable(contents) {
    const tbody = document.querySelector('#contentsTable tbody');
    if (!tbody) return;

    // 添加更新中的视觉提示
    tbody.style.opacity = '0.7';
    tbody.style.transition = 'opacity 0.2s ease';

    if (!contents || contents.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">暂无内容数据</p>
                    </div>
                </td>
            </tr>
        `;
        tbody.style.opacity = '1';
        return;
    }

    let html = '';
    contents.forEach(content => {
        const categoryPath = content.category_path || (content.category ? content.category.name : '未分类');
        const statusSwitch = `
            <div class="form-check form-switch d-flex justify-content-center">
                <input class="form-check-input content-status-switch" type="checkbox"
                       ${content.status == 1 ? 'checked' : ''}
                       data-id="${content.id}"
                       onchange="toggleContentStatus(${content.id}, this)">
            </div>
        `;

        html += `
            <tr>
                <td class="text-center">
                    <div class="form-check d-flex justify-content-center">
                        <input class="form-check-input row-select" type="checkbox" value="${content.id}">
                    </div>
                </td>
                <td class="text-center">
                    <span class="fw-bold">${escapeHtml(content.title)}</span>
                </td>
                <td class="text-center">
                    <small class="text-muted">${escapeHtml(categoryPath)}</small>
                </td>
                <td class="text-center">${statusSwitch}</td>
                <td class="text-center">
                    <input type="number" class="form-control sort-input"
                           value="${content.sort_order || 0}"
                           data-id="${content.id}"
                           data-original="${content.sort_order || 0}"
                           onchange="updateContentSort(${content.id}, this.value)"
                           onblur="updateContentSort(${content.id}, this.value)"
                           min="0"
                           title="直接修改数字即可更新排序">
                </td>
                <td class="text-center">
                    <small class="text-muted">${formatDate(content.create_time)}</small>
                </td>
                <td class="text-center">
                    <div class="category-actions">
                        <button class="category-action-btn edit-btn" onclick="editContent(${content.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="category-action-btn" onclick="viewContent(${content.id})" title="查看" style="background: var(--info-light); color: var(--info-color);">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="category-action-btn delete-btn" onclick="deleteContent(${content.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;

    // 恢复透明度
    setTimeout(() => {
        tbody.style.opacity = '1';
    }, 100);
}

// 更新分页
function updatePagination(paginationHtml) {
    const paginationContainer = document.querySelector('.pagination-nav');
    if (paginationContainer && paginationHtml) {
        paginationContainer.innerHTML = paginationHtml;
    }
}

// 更新统计信息
function updateStats(stats) {
    // 更新各种统计数字
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            element.textContent = stats[key];
        }
    });
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 日期格式化函数
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 更新内容排序
function updateContentSort(id, sortOrder) {
    fetch("/admin/updateContentSort", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `id=${encodeURIComponent(id)}&sort_order=${encodeURIComponent(sortOrder)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 排序更新成功，更新data-original属性
            const input = document.querySelector(`input[data-id="${id}"]`);
            if (input) {
                input.setAttribute('data-original', sortOrder);
            }
            console.log("排序更新成功");
        } else {
            AdminUtils.showMessage(data.msg, "error");
            // 恢复原始值
            const input = document.querySelector(`input[data-id="${id}"]`);
            if (input) {
                input.value = input.getAttribute('data-original');
            }
        }
    })
    .catch(error => {
        console.error("更新排序错误:", error);
        AdminUtils.showMessage("更新排序失败：网络错误", "error");
        // 恢复原始值
        const input = document.querySelector(`input[data-id="${id}"]`);
        if (input) {
            input.value = input.getAttribute('data-original');
        }
    });
}

// 导出内容
function exportContents() {
    console.log("导出内容");
    AdminUtils.showMessage("导出功能开发中...", "info");
}

// 初始化表格
document.addEventListener("DOMContentLoaded", function() {
    TableManager.init("contentsTable", { sortable: true });

    // 恢复页面滚动位置（如果有的话）
    const savedScrollPosition = sessionStorage.getItem('contentListScrollPosition');
    if (savedScrollPosition) {
        setTimeout(() => {
            window.scrollTo({
                top: parseInt(savedScrollPosition),
                behavior: 'instant'
            });
            sessionStorage.removeItem('contentListScrollPosition');
        }, 100);
    }
});

// 保存滚动位置（在页面卸载前）
window.addEventListener('beforeunload', function() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    sessionStorage.setItem('contentListScrollPosition', scrollPosition);
});
</script>

<?php
// 获取输出缓冲的内容并赋值给$content变量
$content = ob_get_clean();

// 包含布局文件
include 'layout.php';
?>
