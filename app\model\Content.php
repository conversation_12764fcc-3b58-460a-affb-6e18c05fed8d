<?php

namespace app\model;

use think\Model;

class Content extends Model
{
    protected $table = 'contents';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'category_id'    => 'int',
        'title'          => 'string',
        'description'    => 'string',
        'content'        => 'string',
        'sort_order'     => 'int',
        'status'         => 'int',
        'create_time'    => 'datetime',
        'update_time'    => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 获取启用的内容
     */
    public static function getEnabled()
    {
        return self::where('status', self::STATUS_ENABLED)
                   ->order('sort_order', 'asc')
                   ->order('id', 'asc')
                   ->select();
    }
    
    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
    

}
