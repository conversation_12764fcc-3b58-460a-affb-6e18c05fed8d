/* 通用提示弹窗组件 */

// 弹窗管理器
const AlertModal = {
    // 当前弹窗元素
    currentModal: null,
    
    // 显示提示弹窗
    show: function(message, type = 'info', title = '', callback = null) {
        // 如果已有弹窗，先关闭
        if (this.currentModal) {
            this.close();
        }
        
        // 创建弹窗HTML
        const modalHtml = this.createModalHtml(message, type, title);
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.currentModal = document.getElementById('alertModal');
        
        // 显示弹窗
        this.currentModal.style.display = 'block';
        
        // 绑定事件
        this.bindEvents(callback);
        
        // 自动聚焦确认按钮
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn');
        if (confirmBtn) {
            setTimeout(() => confirmBtn.focus(), 100);
        }
    },
    
    // 创建弹窗HTML
    createModalHtml: function(message, type, title) {
        const config = this.getTypeConfig(type);
        const modalTitle = title || config.title;
        
        return `
            <div id="alertModal" class="alert-modal">
                <div class="alert-modal-content">
                    <div class="alert-modal-header ${type}">
                        <span class="alert-modal-icon">${config.icon}</span>
                        <h3 class="alert-modal-title">${modalTitle}</h3>
                    </div>
                    <div class="alert-modal-body">
                        <p class="alert-modal-message">${message}</p>
                    </div>
                    <div class="alert-modal-footer">
                        <button class="alert-modal-btn ${type}" onclick="AlertModal.close()">确定</button>
                    </div>
                </div>
            </div>
        `;
    },
    
    // 获取类型配置
    getTypeConfig: function(type) {
        const configs = {
            success: {
                icon: '✅',
                title: '成功'
            },
            error: {
                icon: '❌',
                title: '错误'
            },
            warning: {
                icon: '⚠️',
                title: '警告'
            },
            info: {
                icon: 'ℹ️',
                title: '提示'
            }
        };
        
        return configs[type] || configs.info;
    },
    
    // 绑定事件
    bindEvents: function(callback) {
        if (!this.currentModal) return;

        // 点击遮罩层关闭
        this.currentModal.addEventListener('click', (e) => {
            if (e.target === this.currentModal && !this.currentModal.classList.contains('closing')) {
                this.close(callback);
            }
        });

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape' && this.currentModal && !this.currentModal.classList.contains('closing')) {
                this.close(callback);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // 确认按钮点击
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (!this.currentModal.classList.contains('closing')) {
                    this.close(callback);
                }
            });
        }
    },
    
    // 关闭弹窗
    close: function(callback = null) {
        if (this.currentModal && !this.currentModal.classList.contains('closing')) {
            // 标记为正在关闭，防止重复操作
            this.currentModal.classList.add('closing');

            // 禁用按钮，防止重复点击
            const confirmBtn = this.currentModal.querySelector('.alert-modal-btn');
            if (confirmBtn) {
                confirmBtn.disabled = true;
            }

            // 添加关闭动画类
            this.currentModal.classList.add('alert-modal-closing');

            // 等待动画完成后移除元素
            setTimeout(() => {
                if (this.currentModal && this.currentModal.parentNode) {
                    this.currentModal.parentNode.removeChild(this.currentModal);
                }
                this.currentModal = null;

                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            }, 300);
        }
    },
    
    // 快捷方法
    success: function(message, title = '', callback = null) {
        this.show(message, 'success', title, callback);
    },
    
    error: function(message, title = '', callback = null) {
        this.show(message, 'error', title, callback);
    },
    
    warning: function(message, title = '', callback = null) {
        this.show(message, 'warning', title, callback);
    },
    
    info: function(message, title = '', callback = null) {
        this.show(message, 'info', title, callback);
    },

    // 确认对话框
    confirm: function(message, title = '确认', onConfirm = null, onCancel = null) {
        // 如果已有弹窗，先关闭
        if (this.currentModal) {
            this.close();
        }

        // 创建确认对话框HTML
        const modalHtml = this.createConfirmModalHtml(message, title);

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.currentModal = document.getElementById('alertModal');

        // 显示弹窗
        this.currentModal.style.display = 'block';

        // 绑定确认对话框事件
        this.bindConfirmEvents(onConfirm, onCancel);

        // 自动聚焦确认按钮
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn-confirm');
        if (confirmBtn) {
            setTimeout(() => confirmBtn.focus(), 100);
        }
    },

    // 创建确认对话框HTML
    createConfirmModalHtml: function(message, title) {
        return `
            <div id="alertModal" class="alert-modal">
                <div class="alert-modal-content">
                    <div class="alert-modal-header warning">
                        <span class="alert-modal-icon">⚠️</span>
                        <h3 class="alert-modal-title">${title}</h3>
                    </div>
                    <div class="alert-modal-body">
                        <p class="alert-modal-message">${message}</p>
                    </div>
                    <div class="alert-modal-footer">
                        <button class="alert-modal-btn alert-modal-btn-cancel">取消</button>
                        <button class="alert-modal-btn alert-modal-btn-confirm warning">确定</button>
                    </div>
                </div>
            </div>
        `;
    },

    // 绑定确认对话框事件
    bindConfirmEvents: function(onConfirm, onCancel) {
        if (!this.currentModal) return;

        // 点击遮罩层关闭（相当于取消）
        this.currentModal.addEventListener('click', (e) => {
            if (e.target === this.currentModal && !this.currentModal.classList.contains('closing')) {
                this.close(onCancel);
            }
        });

        // ESC键关闭（相当于取消）
        const escHandler = (e) => {
            if (e.key === 'Escape' && this.currentModal && !this.currentModal.classList.contains('closing')) {
                this.close(onCancel);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // 确认按钮点击
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn-confirm');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (!this.currentModal.classList.contains('closing')) {
                    this.close(onConfirm);
                }
            });
        }

        // 取消按钮点击
        const cancelBtn = this.currentModal.querySelector('.alert-modal-btn-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (!this.currentModal.classList.contains('closing')) {
                    this.close(onCancel);
                }
            });
        }
    }
};

// 全局暴露
window.AlertModal = AlertModal;
