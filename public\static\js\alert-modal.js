/* 通用提示弹窗组件 */

// 弹窗管理器
const AlertModal = {
    // 当前弹窗元素
    currentModal: null,
    
    // 显示提示弹窗
    show: function(message, type = 'info', title = '', callback = null) {
        // 如果已有弹窗，先关闭
        if (this.currentModal) {
            this.close();
        }
        
        // 创建弹窗HTML
        const modalHtml = this.createModalHtml(message, type, title);
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.currentModal = document.getElementById('alertModal');
        
        // 显示弹窗
        this.currentModal.style.display = 'block';
        
        // 绑定事件
        this.bindEvents(callback);
        
        // 自动聚焦确认按钮
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn');
        if (confirmBtn) {
            setTimeout(() => confirmBtn.focus(), 100);
        }
    },
    
    // 创建弹窗HTML
    createModalHtml: function(message, type, title) {
        const config = this.getTypeConfig(type);
        const modalTitle = title || config.title;
        
        return `
            <div id="alertModal" class="alert-modal">
                <div class="alert-modal-content">
                    <div class="alert-modal-header ${type}">
                        <span class="alert-modal-icon">${config.icon}</span>
                        <h3 class="alert-modal-title">${modalTitle}</h3>
                    </div>
                    <div class="alert-modal-body">
                        <p class="alert-modal-message">${message}</p>
                    </div>
                    <div class="alert-modal-footer">
                        <button class="alert-modal-btn ${type}" onclick="AlertModal.close()">确定</button>
                    </div>
                </div>
            </div>
        `;
    },
    
    // 获取类型配置
    getTypeConfig: function(type) {
        const configs = {
            success: {
                icon: '✅',
                title: '成功'
            },
            error: {
                icon: '❌',
                title: '错误'
            },
            warning: {
                icon: '⚠️',
                title: '警告'
            },
            info: {
                icon: 'ℹ️',
                title: '提示'
            }
        };
        
        return configs[type] || configs.info;
    },
    
    // 绑定事件
    bindEvents: function(callback) {
        if (!this.currentModal) return;
        
        // 点击遮罩层关闭
        this.currentModal.addEventListener('click', (e) => {
            if (e.target === this.currentModal) {
                this.close(callback);
            }
        });
        
        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.close(callback);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
        
        // 确认按钮点击
        const confirmBtn = this.currentModal.querySelector('.alert-modal-btn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.close(callback);
            });
        }
    },
    
    // 关闭弹窗
    close: function(callback = null) {
        if (this.currentModal) {
            // 添加关闭动画
            this.currentModal.style.animation = 'fadeOut 0.3s ease';
            
            setTimeout(() => {
                if (this.currentModal && this.currentModal.parentNode) {
                    this.currentModal.parentNode.removeChild(this.currentModal);
                }
                this.currentModal = null;
                
                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            }, 300);
        }
    },
    
    // 快捷方法
    success: function(message, title = '', callback = null) {
        this.show(message, 'success', title, callback);
    },
    
    error: function(message, title = '', callback = null) {
        this.show(message, 'error', title, callback);
    },
    
    warning: function(message, title = '', callback = null) {
        this.show(message, 'warning', title, callback);
    },
    
    info: function(message, title = '', callback = null) {
        this.show(message, 'info', title, callback);
    }
};

// 添加关闭动画CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
`;
document.head.appendChild(style);

// 全局暴露
window.AlertModal = AlertModal;
