<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 卡密兑换管理系统</title>
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            color: #7f8c8d;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-control {
            padding: 15px 20px 15px 50px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1.2rem;
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: #fff;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-1px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .login-footer {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">
                <i class="bi bi-shield-check"></i>
                管理员登录
            </h1>
            <p class="login-subtitle">卡密兑换管理系统</p>
        </div>
        
        <form id="loginForm">
            <div id="alertArea"></div>
            
            <div class="form-group">
                <i class="bi bi-person form-icon"></i>
                <input type="text" 
                       class="form-control" 
                       id="username" 
                       name="username" 
                       placeholder="请输入用户名" 
                       required>
            </div>
            
            <div class="form-group">
                <i class="bi bi-lock form-icon"></i>
                <input type="password" 
                       class="form-control" 
                       id="password" 
                       name="password" 
                       placeholder="请输入密码" 
                       required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                登录
            </button>
        </form>
        
        <div class="login-footer">
            <p>默认账号：admin / 123456</p>
        </div>
    </div>

    <script src="/static/js/common.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');
            const alertArea = document.getElementById('alertArea');
            
            // 清除之前的提示
            alertArea.innerHTML = '';
            
            // 验证输入
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            const originalText = loginBtn.innerHTML;
            loginBtn.innerHTML = '<span class="loading"></span>登录中...';
            loginBtn.disabled = true;
            
            // 发送登录请求
            Utils.ajax({
                url: '/admin/login',
                method: 'POST',
                data: { username, password },
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }).then(response => {
                if (response.code === 1) {
                    showAlert('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = response.url || '/admin/dashboard';
                    }, 1000);
                } else {
                    showAlert(response.msg, 'danger');
                }
            }).catch(error => {
                showAlert('网络错误，请稍后重试', 'danger');
            }).finally(() => {
                // 恢复按钮状态
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
            });
        });
        
        // 显示提示信息
        function showAlert(message, type) {
            const alertArea = document.getElementById('alertArea');
            alertArea.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }
        
        // 回车键提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
