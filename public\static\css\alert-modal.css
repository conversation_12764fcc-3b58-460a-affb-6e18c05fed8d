/* 通用提示弹窗样式 */

/* 弹窗遮罩层 */
.alert-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 弹窗内容容器 */
.alert-modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
}

/* 弹窗头部 */
.alert-modal-header {
    padding: 25px 20px 15px 20px;
    border-radius: 15px 15px 0 0;
}

/* 成功样式 */
.alert-modal-header.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

/* 错误样式 */
.alert-modal-header.error {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
}

/* 警告样式 */
.alert-modal-header.warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

/* 信息样式 */
.alert-modal-header.info {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    color: white;
}

/* 图标样式 */
.alert-modal-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

/* 标题样式 */
.alert-modal-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

/* 弹窗主体 */
.alert-modal-body {
    padding: 20px 25px;
    color: #333;
    line-height: 1.6;
}

.alert-modal-message {
    font-size: 1rem;
    margin: 0;
    word-wrap: break-word;
}

/* 弹窗底部 */
.alert-modal-footer {
    padding: 15px 25px 25px 25px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* 确认按钮 */
.alert-modal-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    min-width: 100px;
}

.alert-modal-btn:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.alert-modal-btn:active {
    transform: translateY(0);
}

.alert-modal-btn:disabled {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none;
}

/* 错误按钮样式 */
.alert-modal-btn.error {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.alert-modal-btn.error:hover {
    background: linear-gradient(45deg, #e74c3c, #dc3545);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* 成功按钮样式 */
.alert-modal-btn.success {
    background: linear-gradient(45deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.alert-modal-btn.success:hover {
    background: linear-gradient(45deg, #20c997, #28a745);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* 警告按钮样式 */
.alert-modal-btn.warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.alert-modal-btn.warning:hover {
    background: linear-gradient(45deg, #fd7e14, #ffc107);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

/* 取消按钮样式 */
.alert-modal-btn-cancel {
    background: linear-gradient(45deg, #6c757d, #495057) !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
}

.alert-modal-btn-cancel:hover {
    background: linear-gradient(45deg, #495057, #6c757d) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
}

/* 关闭动画 */
.alert-modal-closing {
    animation: fadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.alert-modal-closing .alert-modal-content {
    animation: slideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .alert-modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .alert-modal-header {
        padding: 20px 15px 10px 15px;
    }
    
    .alert-modal-body {
        padding: 15px 20px;
    }
    
    .alert-modal-footer {
        padding: 10px 20px 20px 20px;
    }
    
    .alert-modal-icon {
        font-size: 2.5rem;
    }
    
    .alert-modal-title {
        font-size: 1.1rem;
    }
    
    .alert-modal-message {
        font-size: 0.9rem;
    }
}
