<?php
$page_title = '分类管理';
$current_page = 'categories';
$extra_js = ['/static/js/categories.js'];

// 开始输出缓冲
ob_start();

// 构建分类表格内容
$categoryTableContent = '';
if (isset($categories) && !empty($categories)) {
    foreach ($categories as $category) {
        // 检查是否有子分类
        $hasChildren = false;
        foreach ($categories as $child) {
            if ($child['parent_id'] == $category['id']) {
                $hasChildren = true;
                break;
            }
        }

        // 根据层级设置样式类
        $levelClass = 'level-' . $category['level'];
        $childrenClass = $category['parent_id'] > 0 ? 'category-children' : '';

        // 根据层级设置文字颜色
        $textColorClass = '';
        switch ($category['level']) {
            case 1:
                $textColorClass = 'category-level-1';
                break;
            case 2:
                $textColorClass = 'category-level-2';
                break;
            case 3:
                $textColorClass = 'category-level-3';
                break;
        }

        // 设置图标
        $iconClass = $hasChildren ? 'fas fa-folder' : 'fas fa-file';
        if ($category['icon']) {
            $iconClass = $category['icon'];
        }

        // 构建缩进
        $indentHtml = '';
        for ($i = 1; $i < $category['level']; $i++) {
            $indentHtml .= '<div class="category-indent"></div>';
        }

        // 构建切换按钮
        $toggleHtml = '';
        if ($hasChildren) {
            $toggleHtml = '<button class="category-toggle me-2" onclick="toggleCategory(' . $category['id'] . ')">
                <i class="fas fa-chevron-right"></i>
            </button>';
        } else {
            $toggleHtml = '<div class="category-toggle-placeholder me-2"></div>';
        }

        // 构建操作按钮
        $actionButtons = '';
        if ($category['level'] < 3) {
            $actionButtons .= '<button class="category-action-btn add-btn" onclick="addSubCategory(' . $category['id'] . ')" title="添加子分类">
                <i class="fas fa-plus"></i>
            </button>';
        }
        $actionButtons .= '<button class="category-action-btn edit-btn" onclick="editCategory(' . $category['id'] . ')" title="编辑">
            <i class="fas fa-edit"></i>
        </button>
        <button class="category-action-btn delete-btn" onclick="deleteCategory(' . $category['id'] . ')" title="删除">
            <i class="fas fa-trash"></i>
        </button>';

        $categoryTableContent .= '
        <tr class="category-item ' . $levelClass . ' ' . $childrenClass . '"
            data-id="' . $category['id'] . '"
            data-parent="' . $category['parent_id'] . '"
            data-status="' . $category['status'] . '">
            <td class="text-center">
                <div class="form-check">
                    <input class="form-check-input category-checkbox" type="checkbox" value="' . $category['id'] . '" onchange="updateBatchDeleteButton()">
                </div>
            </td>
            <td class="category-name-cell text-left">
                <div class="d-flex align-items-center justify-content-start">
                    ' . $indentHtml . '
                    ' . $toggleHtml . '
                    <div class="category-icon me-2">
                        <i class="' . $iconClass . ' ' . $textColorClass . '"></i>
                    </div>
                    <div class="category-info">
                        <div class="category-name ' . $textColorClass . '" onclick="toggleCategory(' . $category['id'] . ')">' . htmlspecialchars($category['name']) . '</div>
                    </div>
                </div>
            </td>
            <td class="category-status-cell text-center">
                <div class="form-check form-switch d-flex justify-content-center">
                    <input class="form-check-input category-status-switch" type="checkbox"
                           ' . ($category['status'] ? 'checked' : '') . '
                           data-id="' . $category['id'] . '"
                           onchange="toggleCategoryStatus(' . $category['id'] . ', this)">
                </div>
            </td>
            <td class="category-sort-cell text-center">
                <input type="number" class="form-control sort-input"
                       value="' . $category['sort_order'] . '"
                       data-id="' . $category['id'] . '"
                       data-original="' . $category['sort_order'] . '"
                       onchange="updateCategorySort(' . $category['id'] . ', this.value)"
                       onblur="updateCategorySort(' . $category['id'] . ', this.value)"
                       min="0"
                       title="直接修改数字即可更新排序">
            </td>
            <td class="category-actions-cell text-center">
                <div class="category-actions">
                    ' . $actionButtons . '
                </div>
            </td>
        </tr>';
    }
} else {
    $categoryTableContent = '
    <tr>
        <td colspan="5" class="text-center text-muted py-4">
            <i class="fas fa-folder-open fa-3x mb-3"></i>
            <p>暂无分类数据</p>
        </td>
    </tr>';
}

?>

<!-- 页面标题 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="page-title">分类管理</h1>
        <div class="d-flex gap-2">
            <button class="btn btn-danger" id="batchDeleteBtn" onclick="batchDeleteCategories()" style="display: none;">
                <i class="fas fa-trash me-2"></i>批量删除
            </button>
            <button class="btn btn-primary" onclick="addCategory()">
                <i class="fas fa-plus me-2"></i>添加分类
            </button>
        </div>
    </div>
</div>

<!-- 分类概况 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['total']; ?></h3>
                        <p class="stats-label">总分类数</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['enabled']; ?></h3>
                        <p class="stats-label">启用分类</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['disabled']; ?></h3>
                        <p class="stats-label">禁用分类</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="stats-number"><?php echo $stats['with_content']; ?></h3>
                        <p class="stats-label">有内容分类</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 分类表格结构 -->
<div class="chart-container">
    <div class="chart-header">
        <h5 class="chart-title">分类列表</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm" onclick="expandAll()">
                <i class="fas fa-expand-arrows-alt me-1"></i>展开全部
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                <i class="fas fa-compress-arrows-alt me-1"></i>收起全部
            </button>
        </div>
    </div>

    <!-- 功能说明 -->
    <div class="mb-3 p-2 bg-light rounded">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            <strong>操作说明：</strong>
            点击分类名可展开/收起子分类；勾选复选框可批量删除；直接修改排序数字可调整<strong>同级分类</strong>顺序（数字越小越靠前，允许相同排序值，不会影响其他分类的排序）
        </small>
    </div>

    <!-- 表格头部 -->
    <div class="table-container">
        <table class="table category-table">
            <thead>
                <tr>
                    <th width="5%" class="text-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                        </div>
                    </th>
                    <th width="40%" class="text-left">分类名</th>
                    <th width="15%" class="text-center">状态</th>
                    <th width="15%" class="text-center">排序</th>
                    <th width="25%" class="text-center">操作</th>
                </tr>
            </thead>
            <tbody class="category-tree">
                <?php echo $categoryTableContent; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- 分类编辑模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="category_id" name="id">

                    <div class="mb-3">
                        <label for="category_parent" class="form-label">上级分类</label>
                        <select class="form-select" id="category_parent" name="parent_id">
                            <option value="0">无上级分类（顶级分类）</option>
                        </select>
                        <div class="form-text">选择该分类的上级分类，最多支持3级分类</div>
                    </div>

                    <div class="mb-3">
                        <label for="category_name" class="form-label">分类名称</label>
                        <input type="text" class="form-control" id="category_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="category_description" class="form-label">分类描述</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_sort" class="form-label">排序</label>
                                <input type="number" class="form-control" id="category_sort" name="sort_order" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_status" class="form-label">状态</label>
                                <select class="form-select" id="category_status" name="status">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// 获取输出缓冲的内容并赋值给$content变量
$content = ob_get_clean();

// 包含布局文件
include 'layout.php';
?>
