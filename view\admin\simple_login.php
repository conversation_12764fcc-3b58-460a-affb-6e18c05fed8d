<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --text-color: #334155;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            --success-color: #059669;
            --success-light: #d1fae5;
            --danger-color: #dc2626;
            --danger-light: #fee2e2;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            background: var(--bg-secondary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            color: var(--text-color);
        }

        .container {
            background: var(--bg-primary);
            padding: 40px;
            border-radius: 12px;
            box-shadow: var(--box-shadow);
            text-align: center;
            max-width: 400px;
            width: 100%;
            border: 1px solid var(--border-color);
        }

        h1 {
            color: var(--text-color);
            margin-bottom: 30px;
            font-weight: 600;
            font-size: 1.5rem;
        }

        input {
            width: 100%;
            padding: 12px 16px;
            margin: 8px 0;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            box-sizing: border-box;
            transition: var(--transition);
            background: var(--bg-primary);
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        button {
            width: 100%;
            padding: 12px 16px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 16px;
            transition: var(--transition);
        }

        button:hover {
            background: var(--primary-hover);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .note {
            margin-top: 24px;
            color: var(--text-muted);
            font-size: 14px;
        }

        .alert {
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: var(--border-radius);
            font-size: 14px;
        }

        .alert-danger {
            background: var(--danger-light);
            color: var(--danger-color);
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .alert-success {
            background: var(--success-light);
            color: var(--success-color);
            border: 1px solid rgba(5, 150, 105, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员登录</h1>
        
        <form id="loginForm">
            <div id="alertArea"></div>
            
            <input type="text" id="username" name="username" placeholder="用户名" required>
            <input type="password" id="password" name="password" placeholder="密码" required>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <div class="note">
            <p>默认账号：admin / 123456</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');

            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }

            loginBtn.innerHTML = '登录中...';
            loginBtn.disabled = true;

            // 使用XMLHttpRequest确保兼容性
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/login', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    loginBtn.innerHTML = '登录';
                    loginBtn.disabled = false;

                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            if (data.code === 1) {
                                showAlert('登录成功，正在跳转...', 'success');
                                // 使用多种方式确保跳转成功
                                const redirectUrl = data.url || '/admin/dashboard';
                                setTimeout(() => {
                                    window.location.replace(redirectUrl);
                                }, 100);
                                // 备用跳转方式
                                setTimeout(() => {
                                    if (window.location.pathname === '/admin/login') {
                                        window.location.href = redirectUrl;
                                    }
                                }, 1000);
                            } else {
                                showAlert(data.msg || '登录失败', 'danger');
                            }
                        } catch (e) {
                            showAlert('服务器响应错误', 'danger');
                        }
                    } else {
                        showAlert('网络错误，请稍后重试', 'danger');
                    }
                }
            };

            xhr.send('username=' + encodeURIComponent(username) + '&password=' + encodeURIComponent(password));
        });
        
        function showAlert(message, type) {
            const alertArea = document.getElementById('alertArea');
            alertArea.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
        }
    </script>
</body>
</html>
