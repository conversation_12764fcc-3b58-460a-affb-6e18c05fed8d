/* 兑换功能 */

// 获取前端设置的辅助函数
function getFrontendSetting(key, defaultValue = '') {
    return (window.frontendSettings && window.frontendSettings[key]) || defaultValue;
}

// 兑换处理函数
function exchange() {
    const cardNumber = document.getElementById('cardNumber').value.trim();

    if (!cardNumber) {
        AlertModal.warning(getFrontendSetting('empty_card_message', '请输入卡密'));
        return;
    }

    // 显示兑换确认对话框
    AlertModal.confirm(
        getFrontendSetting('exchange_confirm_content', '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！'),
        getFrontendSetting('exchange_confirm_title', '兑换确认'),
        function() {
            // 用户点击确定，执行实际兑换
            performExchange(cardNumber);
        },
        function() {
            // 用户点击取消或关闭，不执行任何操作
            console.log('用户取消了兑换操作');
        }
    );
}

// 执行实际兑换操作
function performExchange(cardNumber) {
    fetch('/exchange', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'card_number=' + encodeURIComponent(cardNumber)
    })
    .then(response => {
        return response.json();
    })
    .then(data => {
        console.log('兑换返回的完整数据:', data);

        if (data.code === 1) {
            // 兑换成功，显示详细信息
            let html = '<div class="exchange-success">';
            html += '<h4>🎉 卡密 ' + cardNumber + ' 兑换成功！</h4>';

            if (data.data) {
                if (data.data.exchange_time) {
                    html += '<p><strong>兑换时间：</strong>' + data.data.exchange_time + '</p>';
                }

                // 显示兑换的内容
                if (data.data.contents && data.data.contents.length > 0) {
                    html += '<div style="margin-top: 15px;">';
                    html += '<h3 class="exchange-content-title">📦 兑换内容</h3>';

                    data.data.contents.forEach(function(content) {
                        // 内容标题
                        html += '<div class="content-item">';
                        html += '<h4>' + content.title + '</h4>';

                        // 显示详细内容 (content字段)
                        if (content.content) {
                            html += formatContentWithLinks(content.content);
                        }

                        // 显示描述（如果有）
                        if (content.description && content.description !== content.title) {
                            html += '<div class="content-description">';
                            html += content.description;
                            html += '</div>';
                        }

                        html += '</div>';
                    });

                    html += '</div>';
                }
            }

            html += '</div>';

            // 添加推广模块
            console.log('推广模块数据:', data.data.promotion);
            if (data.data.promotion && (data.data.promotion.enabled === '1' || data.data.promotion.enabled === 1)) {
                const promotionHtml = generatePromotionModule(data.data.promotion);
                console.log('生成的推广HTML:', promotionHtml);
                if (promotionHtml && promotionHtml.trim() !== '') {
                    html += promotionHtml;
                }
            } else {
                console.log('推广模块未启用或数据为空');
            }

            document.getElementById('result').innerHTML = html;

            // 兑换成功后清空输入框
            document.getElementById('cardNumber').value = '';
        } else {
            // 兑换失败，显示弹窗提示
            AlertModal.error(data.msg);
            // 清空结果区域
            document.getElementById('result').innerHTML = '';
        }
    })
    .catch(error => {
        AlertModal.error(getFrontendSetting('network_error_message', '网络错误，请稍后重试'));
        // 清空结果区域
        document.getElementById('result').innerHTML = '';
    });
}

// 格式化内容并处理链接
function formatContentWithLinks(content) {
    // 将换行符转换为<br>，并保持原有格式
    let formattedContent = content
        .replace(/\n/g, '<br>')
        .replace(/\r/g, '');

    // 使用更精确的方法处理链接
    // 先标记已处理的链接，避免重复处理
    let linkCounter = 0;
    const linkPlaceholders = {};

    // 处理带协议的链接
    formattedContent = formattedContent.replace(/(https?:\/\/[^\s<>\[\]]+)/gi, function(match) {
        const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
        linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + match + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + match + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
        return placeholder;
    });

    // 处理不带协议的网址
    formattedContent = formattedContent.replace(/\b((?:www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*\.[a-zA-Z]{2,}(?:\/[^\s<>\[\]]*)?)\b/gi, function(match) {
        // 检查是否已经在占位符中
        if (match.includes('___LINK_PLACEHOLDER_')) return match;

        const fullUrl = match.startsWith('www.') ? 'https://' + match : 'https://' + match;
        const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
        linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + fullUrl + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + fullUrl + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
        return placeholder;
    });

    // 恢复链接占位符
    for (const placeholder in linkPlaceholders) {
        formattedContent = formattedContent.replace(placeholder, linkPlaceholders[placeholder]);
    }

    // 处理其他高亮内容
    formattedContent = formattedContent
        // 识别微信号并高亮显示
        .replace(/(微信[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>')
        // 识别QQ号并高亮显示
        .replace(/(QQ[：:]?\s*\d+)/gi, '<span class="contact-info">$1</span>')
        // 识别其他联系方式
        .replace(/(客服[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>');

    return '<div class="content-detail">' + formattedContent + '</div>';
}

// 生成推广模块HTML
function generatePromotionModule(promotion) {

    let html = '<div class="promotion-module">';

    // 标题
    if (promotion.title && promotion.title.trim() !== '') {
        html += '<div class="promotion-title">' + promotion.title + '</div>';
    }

    // 按钮组
    let hasButtons = false;
    let buttonsHtml = '<div class="promotion-buttons">';

    if (promotion.buttons && Array.isArray(promotion.buttons)) {
        promotion.buttons.forEach(function(button) {
            if (button.text && button.text.trim() !== '' && button.url && button.url.trim() !== '' && button.url !== '#') {
                buttonsHtml += '<a href="' + button.url + '" target="_blank" class="promotion-btn">' + button.text + '</a>';
                hasButtons = true;
            }
        });
    }
    buttonsHtml += '</div>';

    if (hasButtons) {
        html += buttonsHtml;
    }

    // 联系方式
    if (promotion.contact && promotion.contact.text && promotion.contact.text.trim() !== '' &&
        promotion.contact.value && promotion.contact.value.trim() !== '') {
        html += '<div class="promotion-contact">' + promotion.contact.text + ' <span class="promotion-contact-value">' + promotion.contact.value + '</span></div>';
    }

    html += '</div>';

    // 如果没有任何内容，返回空字符串
    if (!hasButtons && (!promotion.title || promotion.title.trim() === '') &&
        (!promotion.contact || !promotion.contact.text || promotion.contact.text.trim() === '' ||
         !promotion.contact.value || promotion.contact.value.trim() === '')) {
        return '';
    }

    return html;
}

// 复制链接功能
function copyLink(url) {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = url;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        // 尝试使用现代API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(url).then(function() {
                showCopySuccess();
            }).catch(function() {
                // 如果现代API失败，使用传统方法
                fallbackCopy();
            });
        } else {
            // 使用传统方法
            fallbackCopy();
        }
    } catch (err) {
        fallbackCopy();
    }

    function fallbackCopy() {
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess();
            } else {
                showCopyError();
            }
        } catch (err) {
            showCopyError();
        }
    }

    document.body.removeChild(textArea);
}

// 显示复制成功提示
function showCopySuccess() {
    const successDiv = document.createElement('div');
    successDiv.className = 'copy-success';
    successDiv.innerHTML = getFrontendSetting('copy_success_message', '✅ 链接复制成功！');
    document.body.appendChild(successDiv);

    setTimeout(function() {
        if (document.body.contains(successDiv)) {
            document.body.removeChild(successDiv);
        }
    }, 2000);
}

// 显示复制失败提示
function showCopyError() {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'copy-success';
    errorDiv.style.background = '#dc3545';
    errorDiv.innerHTML = getFrontendSetting('copy_error_message', '❌ 复制失败，请手动复制');
    document.body.appendChild(errorDiv);

    setTimeout(function() {
        if (document.body.contains(errorDiv)) {
            document.body.removeChild(errorDiv);
        }
    }, 2000);
}
