# 卡密兑换系统数据表设计

## 1. 分类表 (categories)
支持三级分类的树状结构

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 分类ID |
| parent_id | int | 11 | 0 | 父级分类ID，0为顶级分类 |
| name | varchar | 100 | - | 分类名称 |
| description | text | - | NULL | 分类描述 |
| icon | varchar | 100 | NULL | 分类图标 |
| sort_order | int | 11 | 0 | 排序权重 |
| level | tinyint | 1 | 1 | 分类层级：1-3级 |
| path | varchar | 500 | NULL | 分类路径，如：1,2,3 |
| status | tinyint | 1 | 1 | 状态：1启用，0禁用 |
| create_time | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP | 更新时间 |

## 2. 内容表 (contents)

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 内容ID |
| category_id | int | 11 | - | 分类ID |
| title | varchar | 200 | - | 内容标题 |
| description | text | - | NULL | 内容描述 |
| content | longtext | - | NULL | 详细内容 |
| sort_order | int | 11 | 0 | 排序权重 |
| status | tinyint | 1 | 1 | 状态：1启用，0禁用 |
| create_time | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP | 更新时间 |

## 3. 卡密表 (cards)
存储生成的卡密信息

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 卡密ID |
| card_number | varchar | 50 | - | 卡密号码 |
| card_type | varchar | 50 | - | 卡密类型 |
| batch_id | varchar | 50 | NULL | 批次ID |
| category_id | int | 11 | NULL | 关联分类ID |
| content_ids | text | - | NULL | 可兑换内容ID列表（JSON格式） |
| value | decimal | 10,2 | 0.00 | 卡密面值 |
| valid_days | int | 11 | 0 | 有效天数，0为永久 |
| max_use_count | int | 11 | 1 | 最大使用次数 |
| used_count | int | 11 | 0 | 已使用次数 |
| status | tinyint | 1 | 1 | 状态：1未使用，2已使用，0已禁用 |
| used_time | datetime | - | NULL | 首次使用时间 |
| used_ip | varchar | 45 | NULL | 使用IP |
| expire_time | datetime | - | NULL | 过期时间 |
| remark | varchar | 500 | NULL | 备注 |
| create_time | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP | 更新时间 |

## 4. 兑换记录表 (exchange_records)
记录卡密兑换历史

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 记录ID |
| card_id | int | 11 | - | 卡密ID |
| card_number | varchar | 50 | - | 卡密号码 |
| content_ids | text | - | NULL | 兑换的内容ID列表（JSON格式） |
| exchange_ip | varchar | 45 | NULL | 兑换IP |
| user_agent | text | - | NULL | 用户代理 |
| exchange_time | datetime | - | CURRENT_TIMESTAMP | 兑换时间 |
| status | tinyint | 1 | 1 | 状态：1成功，0失败 |
| remark | varchar | 500 | NULL | 备注 |

## 5. 管理员表 (admin_users)
系统管理员账号

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 管理员ID |
| username | varchar | 50 | - | 用户名 |
| password | varchar | 255 | - | 密码哈希 |
| nickname | varchar | 100 | NULL | 昵称 |
| email | varchar | 100 | NULL | 邮箱 |
| phone | varchar | 20 | NULL | 手机号 |
| avatar | varchar | 255 | NULL | 头像 |
| role | varchar | 50 | 'admin' | 角色 |
| permissions | text | - | NULL | 权限列表（JSON格式） |
| status | tinyint | 1 | 1 | 状态：1启用，0禁用 |
| last_login_time | datetime | - | NULL | 最后登录时间 |
| last_login_ip | varchar | 45 | NULL | 最后登录IP |
| create_time | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP | 更新时间 |

## 6. 系统设置表 (settings)
系统配置参数

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | int | 11 | AUTO_INCREMENT | 设置ID |
| key | varchar | 100 | - | 配置键名 |
| value | text | - | NULL | 配置值 |
| type | varchar | 20 | 'string' | 数据类型：string,int,bool,json |
| group | varchar | 50 | 'system' | 配置分组 |
| title | varchar | 100 | NULL | 配置标题 |
| description | varchar | 500 | NULL | 配置描述 |
| sort_order | int | 11 | 0 | 排序权重 |
| create_time | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP | 更新时间 |

## 索引设计

### categories表索引
- PRIMARY KEY (id)
- KEY idx_parent_id (parent_id)
- KEY idx_status_sort (status, sort_order)
- KEY idx_level (level)

### contents表索引
- PRIMARY KEY (id)
- KEY idx_category_id (category_id)
- KEY idx_status_sort (status, sort_order)
- KEY idx_featured (is_featured)

### cards表索引
- PRIMARY KEY (id)
- UNIQUE KEY uk_card_number (card_number)
- KEY idx_status (status)
- KEY idx_batch_id (batch_id)
- KEY idx_category_id (category_id)
- KEY idx_expire_time (expire_time)

### exchange_records表索引
- PRIMARY KEY (id)
- KEY idx_card_id (card_id)
- KEY idx_card_number (card_number)
- KEY idx_exchange_time (exchange_time)
- KEY idx_status (status)

### admin_users表索引
- PRIMARY KEY (id)
- UNIQUE KEY uk_username (username)
- KEY idx_status (status)

### settings表索引
- PRIMARY KEY (id)
- UNIQUE KEY uk_key (key)
- KEY idx_group (group)
- KEY idx_sort_order (sort_order)
