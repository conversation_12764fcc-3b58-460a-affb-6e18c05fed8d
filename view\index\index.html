<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红嘴鸥教育 - 电子资料兑换系统</title>
    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/user.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="user-page">
        <div class="exchange-container">
            <h1 class="exchange-title">红嘴鸥教育</h1>
            <h2 class="exchange-subtitle">电子资料兑换系统</h2>
            
            <!-- 兑换表单 -->
            <form id="exchangeForm">
                <div class="input-group">
                    <input type="text" 
                           class="form-control" 
                           id="cardNumber" 
                           name="card_number" 
                           placeholder="请输入卡密" 
                           maxlength="50"
                           autocomplete="off">
                    <button type="submit" class="btn">立即兑换</button>
                </div>
            </form>
            
            <!-- 查询按钮 -->
            <button type="button" class="query-btn" id="queryBtn">兑换记录查询</button>
            
            <!-- 结果显示区域 -->
            <div id="resultArea" style="display: none;"></div>
            
            <!-- 说明文字 -->
            <div class="exchange-note">
                <strong>说明：</strong> 此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，
                点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888
            </div>
        </div>
    </div>

    <script>
        // 测试JavaScript是否执行
        console.log('JavaScript开始执行');
        alert('JavaScript正在执行！');

        // 简化的Ajax函数
        function ajax(options) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(options.method || 'GET', options.url);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            resolve(JSON.parse(xhr.responseText));
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status));
                    }
                };

                xhr.onerror = function() {
                    reject(new Error('网络错误'));
                };

                if (options.data) {
                    const formData = new URLSearchParams();
                    for (const key in options.data) {
                        formData.append(key, options.data[key]);
                    }
                    xhr.send(formData.toString());
                } else {
                    xhr.send();
                }
            });
        }

        // 简化的验证函数
        function validateCardNumber(cardNumber) {
            if (!cardNumber || cardNumber.trim() === '') {
                return { valid: false, message: '请输入卡密' };
            }

            const trimmed = cardNumber.trim();
            if (trimmed.length < 4) {
                return { valid: false, message: '卡密长度不能少于4位' };
            }

            return { valid: true, message: '' };
        }

        // 兑换表单处理
        document.getElementById('exchangeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('表单提交事件触发');
            
            const cardNumber = document.getElementById('cardNumber').value.trim();
            const submitBtn = this.querySelector('button[type="submit"]');
            const resultArea = document.getElementById('resultArea');
            
            // 验证卡密
            const validation = validateCardNumber(cardNumber);
            if (!validation.valid) {
                showResult(validation.message, 'error');
                return;
            }
            
            // 显示加载状态
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="loading"></span>兑换中...';
            submitBtn.disabled = true;
            
            // 发送兑换请求
            console.log('准备发送Ajax请求，卡密:', cardNumber);
            ajax({
                url: '/exchange',
                method: 'POST',
                data: { card_number: cardNumber },
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }).then(response => {
                console.log('兑换响应原始数据:', response); // 调试信息
                console.log('响应类型:', typeof response); // 调试信息
                console.log('响应内容:', JSON.stringify(response)); // 调试信息

                if (response.code === 1) {
                    // 兑换成功
                    let html = `
                        <div class="exchange-result success">
                            <h4>🎉 卡密 ${cardNumber} 兑换成功！</h4>
                            <p><strong>卡密类型：</strong>${response.data.card_type || '通用卡密'}</p>
                            <p><strong>卡密面值：</strong>￥${response.data.value || '0.00'}</p>
                            <p><strong>兑换时间：</strong>${response.data.exchange_time || new Date().toLocaleString()}</p>
                    `;

                    console.log('contents数据:', response.data.contents); // 调试信息
                    if (response.data.contents && response.data.contents.length > 0) {
                        html += '<h3 class="exchange-content-title">📦 兑换内容</h3><div style="text-align: left;">';
                        response.data.contents.forEach((content, index) => {
                            console.log(`内容${index}:`, content); // 调试信息
                            html += `
                                <div style="padding: 15px; margin: 10px 0; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                                    <h5 style="margin: 0 0 10px 0; color: #2c3e50; font-weight: bold;">${content.title || '未知标题'}</h5>
                                    <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">
                                        <i class="bi bi-folder"></i> ${content.category_name || '未分类'} |
                                        <i class="bi bi-file-earmark"></i> ${content.file_size_format || '未知大小'}
                                    </p>
                                    ${content.description ? `<p style="margin: 8px 0; color: #495057; font-style: italic;">${content.description}</p>` : ''}
                                    ${content.content ? `
                                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #dee2e6;">
                                            <h6 style="margin: 0 0 8px 0; color: #495057;">详细内容：</h6>
                                            <div style="color: #212529; line-height: 1.6; white-space: pre-wrap;">${content.content}</div>
                                        </div>
                                    ` : ''}
                                    <div style="margin-top: 10px;">
                                        <a href="${content.download_url || '#'}" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="bi bi-download"></i> 立即下载
                                        </a>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    } else {
                        html += '<p style="color: #f39c12;">⚠️ 该卡密暂无可下载内容</p>';
                    }

                    html += '</div>';
                    showResult(html, 'success');

                    // 清空输入框
                    document.getElementById('cardNumber').value = '';
                } else {
                    // 兑换失败
                    showResult(response.msg, 'error');
                }
            }).catch(error => {
                console.error('Ajax请求错误:', error); // 调试信息
                showResult('网络错误，请稍后重试', 'error');
            }).finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
        
        // 查询按钮处理
        document.getElementById('queryBtn').addEventListener('click', function() {
            const cardNumber = document.getElementById('cardNumber').value.trim();
            
            // 验证卡密
            const validation = validateCardNumber(cardNumber);
            if (!validation.valid) {
                showResult(validation.message, 'error');
                return;
            }
            
            // 显示加载状态
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading"></span>查询中...';
            this.disabled = true;
            
            // 发送查询请求
            ajax({
                url: '/query',
                method: 'POST',
                data: { card_number: cardNumber }
            }).then(response => {
                if (response.code === 1) {
                    // 查询成功
                    let html = `
                        <div class="exchange-result success">
                            <h4>📋 查询结果</h4>
                    `;

                    // 显示卡密信息
                    if (response.data.card_info) {
                        const cardInfo = response.data.card_info;
                        html += `
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; text-align: left;">
                                <h6 style="margin: 0 0 10px 0; color: #2c3e50;">卡密信息</h6>
                                <p style="margin: 5px 0;"><strong>类型：</strong>${cardInfo.card_type}</p>
                                <p style="margin: 5px 0;"><strong>面值：</strong>￥${cardInfo.value}</p>
                                <p style="margin: 5px 0;"><strong>状态：</strong><span class="status-badge ${cardInfo.status === '已使用' ? 'danger' : cardInfo.status === '未使用' ? 'success' : 'warning'}">${cardInfo.status}</span></p>
                                <p style="margin: 5px 0;"><strong>有效期：</strong>${cardInfo.expire_time}</p>
                                ${cardInfo.used_time ? `<p style="margin: 5px 0;"><strong>使用时间：</strong>${cardInfo.used_time}</p>` : ''}
                            </div>
                        `;
                    }

                    // 显示关联内容
                    if (response.data.contents && response.data.contents.length > 0) {
                        html += `
                            <div style="margin-top: 15px;">
                                <h6 style="color: #2c3e50; margin-bottom: 10px;">可下载资料</h6>
                                <div style="text-align: left;">
                        `;

                        response.data.contents.forEach(content => {
                            html += `
                                <div style="padding: 15px; margin: 10px 0; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                                    <h5 style="margin: 0 0 10px 0; color: #2c3e50; font-weight: bold;">${content.title}</h5>
                                    <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">
                                        <i class="bi bi-folder"></i> ${content.category_name} |
                                        <i class="bi bi-file-earmark"></i> ${content.file_size_format}
                                    </p>
                                    ${content.description ? `<p style="margin: 8px 0; color: #495057; font-style: italic;">${content.description}</p>` : ''}
                                    ${content.content ? `
                                        <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #dee2e6;">
                                            <h6 style="margin: 0 0 8px 0; color: #495057;">详细内容：</h6>
                                            <div style="color: #212529; line-height: 1.6; white-space: pre-wrap;">${content.content}</div>
                                        </div>
                                    ` : ''}
                                    <div style="margin-top: 10px;">
                                        <a href="${content.download_url}" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="bi bi-download"></i> 立即下载
                                        </a>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div></div>';
                    }

                    // 显示兑换记录
                    if (response.data.records && response.data.records.length > 0) {
                        html += `
                            <div style="margin-top: 15px;">
                                <h6 style="color: #2c3e50; margin-bottom: 10px;">兑换记录</h6>
                                <div style="max-height: 200px; overflow-y: auto;">
                        `;

                        response.data.records.forEach(record => {
                            const statusClass = record.status === '成功' ? 'success' : 'danger';
                            html += `
                                <div style="padding: 10px; border-bottom: 1px solid #eee; text-align: left;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="font-weight: 500;">${record.exchange_time}</span>
                                        <span class="status-badge ${statusClass}">${record.status}</span>
                                    </div>
                                    <p style="margin: 5px 0; color: #6c757d; font-size: 0.9rem;">${record.remark}</p>
                                    <small style="color: #999;">IP: ${record.ip}</small>
                                </div>
                            `;
                        });

                        html += '</div></div>';
                    }

                    html += '</div>';
                    showResult(html, 'success');
                } else {
                    // 查询失败
                    showResult(response.msg, 'error');
                }
            }).catch(error => {
                showResult('网络错误，请稍后重试', 'error');
            }).finally(() => {
                // 恢复按钮状态
                this.innerHTML = originalText;
                this.disabled = false;
            });
        });
        
        // 显示结果
        function showResult(content, type) {
            const resultArea = document.getElementById('resultArea');
            console.log('showResult调用:', {content: content, type: type}); // 调试信息

            if (typeof content === 'string') {
                // 如果内容包含HTML标签，直接使用；否则包装在div中
                if (content.includes('<div') || content.includes('<p')) {
                    resultArea.innerHTML = content;
                } else {
                    resultArea.innerHTML = `<div class="exchange-result ${type}">${content}</div>`;
                }
            } else {
                resultArea.innerHTML = content;
            }

            resultArea.style.display = 'block';
            console.log('resultArea内容已设置:', resultArea.innerHTML.substring(0, 200)); // 调试信息

            // 滚动到结果区域
            resultArea.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 回车键提交
        document.getElementById('cardNumber').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('exchangeForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
