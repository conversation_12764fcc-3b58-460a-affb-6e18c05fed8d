<?php
$page_title = '卡密设置';
$current_page = 'card-settings';

// 设置默认值
$cardSettings = $cardSettings ?? [
    'card_generate_count' => 1,
    'card_length' => 8,
    'card_character_type' => 'mixed',
    'card_usage_limit' => 1,
    'card_prefix' => 'HZO-',
    'card_suffix' => '',
    'card_validity_days' => 0,
    'card_separator' => '',
    'card_case_sensitive' => 1,
    'card_auto_delete' => 1,
    'card_log_usage' => 1,
    'card_description' => '此卡密仅限本站使用，请妥善保管。如有问题请联系客服。',
    'card_success_title' => '卡密生成成功',
    'card_success_content' => '内容跟标题\n卡密：变量\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）',
];

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">卡密设置</h2>
        <p class="text-muted mb-0">配置卡密生成和管理的相关参数</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveCardSettings()">
            <i class="fas fa-save me-2"></i>保存设置
        </button>
    </div>
</div>

<!-- 卡密设置标签页 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="cardSettingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="generation-tab" data-bs-toggle="tab" data-bs-target="#generation" type="button" role="tab">
                <i class="fas fa-cogs me-2"></i>生成设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="popup-tab" data-bs-toggle="tab" data-bs-target="#popup" type="button" role="tab">
                <i class="fas fa-window-restore me-2"></i>弹窗设置
            </button>
        </li>
    </ul>

    <div class="tab-content" id="cardSettingsTabContent">
        <!-- 生成设置 -->
        <div class="tab-pane fade show active" id="generation" role="tabpanel">
            <div class="p-4">
                <form id="cardForm">
                    <div class="row g-4">
                <div class="col-md-6">
                    <label class="form-label">每次生成数量</label>
                    <input type="number" class="form-control" name="card_generate_count" value="' . $cardSettings['card_generate_count'] . '" min="1" max="1000">
                    <div class="form-text">单次批量生成卡密的数量</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">卡密长度</label>
                    <input type="number" class="form-control" name="card_length" value="' . $cardSettings['card_length'] . '" min="4" max="32">
                    <div class="form-text">卡密字符串的长度（不包含前缀）</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">字符组合</label>
                    <select class="form-select" name="card_character_type">
                        <option value="mixed" ' . ($cardSettings['card_character_type'] === 'mixed' ? 'selected' : '') . '>数字+字母</option>
                        <option value="numbers" ' . ($cardSettings['card_character_type'] === 'numbers' ? 'selected' : '') . '>仅数字</option>
                        <option value="letters" ' . ($cardSettings['card_character_type'] === 'letters' ? 'selected' : '') . '>仅字母</option>
                        <option value="uppercase" ' . ($cardSettings['card_character_type'] === 'uppercase' ? 'selected' : '') . '>仅大写字母</option>
                        <option value="lowercase" ' . ($cardSettings['card_character_type'] === 'lowercase' ? 'selected' : '') . '>仅小写字母</option>
                        <option value="alphanumeric" ' . ($cardSettings['card_character_type'] === 'alphanumeric' ? 'selected' : '') . '>数字+大写字母</option>
                    </select>
                    <div class="form-text">卡密包含的字符类型</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">使用次数</label>
                    <input type="number" class="form-control" name="card_usage_limit" value="' . $cardSettings['card_usage_limit'] . '" min="1" max="999">
                    <div class="form-text">每个卡密可以使用的次数</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">卡密前缀</label>
                    <input type="text" class="form-control" name="card_prefix" value="' . htmlspecialchars($cardSettings['card_prefix']) . '" maxlength="10">
                    <div class="form-text">卡密的固定前缀，留空则无前缀</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">卡密后缀</label>
                    <input type="text" class="form-control" name="card_suffix" value="' . htmlspecialchars($cardSettings['card_suffix']) . '" maxlength="10">
                    <div class="form-text">卡密的固定后缀，留空则无后缀</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">有效期设置</label>
                    <div class="input-group">
                        <input type="number" class="form-control" name="card_validity_days" value="' . $cardSettings['card_validity_days'] . '" min="0" max="3650">
                        <span class="input-group-text">天</span>
                    </div>
                    <div class="form-text">卡密的有效期，0表示永不过期</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">分隔符</label>
                    <select class="form-select" name="card_separator">
                        <option value="" ' . ($cardSettings['card_separator'] === '' ? 'selected' : '') . '>无分隔符</option>
                        <option value="-" ' . ($cardSettings['card_separator'] === '-' ? 'selected' : '') . '>短横线（-）</option>
                        <option value="_" ' . ($cardSettings['card_separator'] === '_' ? 'selected' : '') . '>下划线（_）</option>
                        <option value="." ' . ($cardSettings['card_separator'] === '.' ? 'selected' : '') . '>点号（.）</option>
                    </select>
                    <div class="form-text">卡密中间的分隔符，每4位插入一个</div>
                </div>
                <div class="col-12">
                    <label class="form-label">
                        <i class="fas fa-eye me-2"></i>卡密格式预览
                    </label>
                    <div class="text-center py-3">
                        <code id="cardPreview">HZO-A1B2C3D4</code>
                    </div>
                    <div class="form-text text-center">根据当前设置生成的卡密格式示例</div>
                </div>

                <!-- 功能开关设置 -->
                <div class="col-12">
                    <h6 class="settings-section-title">
                        <i class="fas fa-toggle-on me-2"></i>功能开关
                    </h6>
                </div>
                <div class="col-md-4">
                    <div class="card card-settings-switch h-100">
                        <div class="card-body text-center">
                            <div class="form-check form-switch d-flex justify-content-center mb-2">
                                <input class="form-check-input" type="checkbox" name="card_case_sensitive" ' . ($cardSettings['card_case_sensitive'] ? 'checked' : '') . '>
                            </div>
                            <h6 class="card-title mb-2">区分大小写</h6>
                            <p class="card-text text-muted">验证卡密时是否区分字母大小写</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card card-settings-switch h-100">
                        <div class="card-body text-center">
                            <div class="form-check form-switch d-flex justify-content-center mb-2">
                                <input class="form-check-input" type="checkbox" name="card_auto_delete" ' . ($cardSettings['card_auto_delete'] ? 'checked' : '') . '>
                            </div>
                            <h6 class="card-title mb-2">使用后自动删除</h6>
                            <p class="card-text text-muted">卡密使用完毕后是否自动从数据库删除</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card card-settings-switch h-100">
                        <div class="card-body text-center">
                            <div class="form-check form-switch d-flex justify-content-center mb-2">
                                <input class="form-check-input" type="checkbox" name="card_log_usage" ' . ($cardSettings['card_log_usage'] ? 'checked' : '') . '>
                            </div>
                            <h6 class="card-title mb-2">记录使用日志</h6>
                            <p class="card-text text-muted">是否记录卡密的使用情况和用户信息</p>
                        </div>
                    </div>
                </div>
                        <div class="col-12">
                            <label class="form-label">卡密说明</label>
                            <textarea class="form-control" name="card_description" rows="3" placeholder="请输入卡密使用说明...">' . htmlspecialchars($cardSettings['card_description']) . '</textarea>
                            <div class="form-text">显示给用户的卡密使用说明</div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 弹窗设置 -->
        <div class="tab-pane fade" id="popup" role="tabpanel">
            <div class="p-4">
                <div class="row g-4">
                    <div class="col-12">
                    <label class="form-label">弹窗标题</label>
                    <input type="text" class="form-control" name="card_success_title" value="' . htmlspecialchars($cardSettings['card_success_title']) . '" maxlength="50">
                    <div class="form-text">生成成功弹窗的标题</div>
                </div>
                    <div class="col-12">
                        <label class="form-label">弹窗内容</label>
                        <textarea class="form-control" name="card_success_content" rows="10" placeholder="请输入弹窗内容模板">' . htmlspecialchars($cardSettings['card_success_content']) . '</textarea>
                        <div class="form-text">
                            弹窗显示的完整内容模板，支持动态变量替换
                        </div>

                        <!-- 变量使用说明 -->
                        <div class="alert alert-info mt-2">
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-info-circle me-2"></i>可用变量说明
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>动态变量：</strong></p>
                                    <ul class="mb-0 small">
                                        <li><code>{CONTENT_TITLES}</code> - 关联的内容标题（多个用顿号分隔）</li>
                                        <li><code>{CARD_NUMBERS}</code> - 生成的卡密号码（多个换行显示）</li>
                                        <li><code>{CARD_COUNT}</code> - 生成的卡密数量</li>
                                        <li><code>{BATCH_ID}</code> - 卡密批次ID</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>使用示例：</strong></p>
                                    <div class="small">
                                        <code>内容标题：{CONTENT_TITLES}</code><br>
                                        <code>卡密：{CARD_NUMBERS}</code><br>
                                        <code>共生成 {CARD_COUNT} 个卡密</code><br>
                                        <code>批次号：{BATCH_ID}</code>
                                    </div>
                                </div>
                            </div>
                            <hr class="my-2">
                            <p class="mb-0 small text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                <strong>提示：</strong>变量会在生成卡密成功后自动替换为实际内容，请确保变量名称正确（区分大小写）
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
';

?>

<script>

// 保存卡密设置
function saveCardSettings() {
    const formData = new FormData();

    // 收集生成设置表单数据
    const form = document.getElementById("cardForm");
    const formElements = form.querySelectorAll("input, select, textarea");
    formElements.forEach(element => {
        if (element.type === 'checkbox') {
            formData.set(element.name, element.checked ? "1" : "0");
        } else {
            formData.set(element.name, element.value);
        }
    });

    // 收集弹窗设置数据
    const popupInputs = document.querySelectorAll("#popup input, #popup textarea");
    popupInputs.forEach(element => {
        formData.set(element.name, element.value);
    });

    // 发送AJAX请求
    fetch('/admin/saveCardSettings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            AdminUtils.showMessage(data.msg, "success");
        } else {
            AdminUtils.showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        AdminUtils.showMessage("保存失败，请重试", "error");
    });
}

// 更新卡密预览
function updateCardPreview() {
    const prefix = document.querySelector("input[name=card_prefix]").value;
    const length = parseInt(document.querySelector("input[name=card_length]").value) || 8;
    const separator = document.querySelector("select[name=card_separator]").value;
    const suffix = document.querySelector("input[name=card_suffix]").value;

    let preview = "";
    if (prefix) preview += prefix;

    // 生成示例字符
    const chars = "A1B2C3D4E5F6G7H8";
    let cardPart = "";
    for (let i = 0; i < length; i++) {
        if (separator && i > 0 && i % 4 === 0) {
            cardPart += separator;
        }
        cardPart += chars[i % chars.length];
    }
    preview += cardPart;

    if (suffix) preview += suffix;

    document.getElementById("cardPreview").textContent = preview;
}

// 绑定事件
document.addEventListener("DOMContentLoaded", function() {
    const inputs = document.querySelectorAll("input[name=card_prefix], input[name=card_length], select[name=card_separator], input[name=card_suffix]");
    inputs.forEach(input => {
        input.addEventListener("input", updateCardPreview);
        input.addEventListener("change", updateCardPreview);
    });

    updateCardPreview();
});
</script>

<?php
include 'layout.php';
?>
