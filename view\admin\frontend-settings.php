<?php
$page_title = '前端设置';
$current_page = 'frontend-settings';

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">前端设置</h2>
        <p class="text-muted mb-0">配置前端页面的显示内容和样式</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveFrontendSettings()">
            <i class="fas fa-save me-2"></i>保存设置
        </button>
    </div>
</div>

<!-- 前端设置 -->
<div class="chart-container">
    <div class="p-4">
        <form id="frontendForm">
            <div class="row g-4">
                <!-- 网站基本信息 -->
                <div class="col-12">
                    <h5 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-globe me-2"></i>网站基本信息
                    </h5>
                </div>
                <div class="col-md-6">
                    <label class="form-label">网站Logo</label>
                    <input type="file" class="form-control" name="site_logo" accept="image/*">
                    <div class="form-text">建议尺寸：200x60像素，支持PNG、JPG格式</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">网站标题</label>
                    <input type="text" class="form-control" name="site_title" placeholder="请输入网站标题">
                    <div class="form-text">显示在浏览器标题栏和页面头部</div>
                </div>
                <div class="col-12">
                    <label class="form-label">网站描述</label>
                    <textarea class="form-control" name="site_description" rows="3" placeholder="请输入网站描述"></textarea>
                    <div class="form-text">显示在页面头部的描述信息</div>
                </div>

                <!-- 推广模块设置 -->
                <div class="col-12 mt-4">
                    <h5 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-bullhorn me-2"></i>推广模块设置
                    </h5>
                </div>
                <div class="col-12">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" name="promotion_enabled" id="promotionEnabled" value="1">
                        <label class="form-check-label" for="promotionEnabled">启用推广模块</label>
                    </div>
                    <div class="form-text">兑换成功后是否显示推广模块</div>
                </div>
                <div class="col-12">
                    <label class="form-label">推广标题</label>
                    <input type="text" class="form-control" name="promotion_title" placeholder="您还可以点击以下按钮获取更多免费资源">
                    <div class="form-text">推广模块的标题文字</div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">按钮1文字</label>
                    <input type="text" class="form-control" name="promotion_btn1_text" placeholder="电子资料包">
                </div>
                <div class="col-md-8">
                    <label class="form-label">按钮1链接</label>
                    <input type="url" class="form-control" name="promotion_btn1_url" placeholder="https://example.com">
                </div>
                <div class="col-md-4">
                    <label class="form-label">按钮2文字</label>
                    <input type="text" class="form-control" name="promotion_btn2_text" placeholder="免费网课">
                </div>
                <div class="col-md-8">
                    <label class="form-label">按钮2链接</label>
                    <input type="url" class="form-control" name="promotion_btn2_url" placeholder="https://example.com">
                </div>
                <div class="col-md-4">
                    <label class="form-label">按钮3文字</label>
                    <input type="text" class="form-control" name="promotion_btn3_text" placeholder="官方网站">
                </div>
                <div class="col-md-8">
                    <label class="form-label">按钮3链接</label>
                    <input type="url" class="form-control" name="promotion_btn3_url" placeholder="https://example.com">
                </div>
                <div class="col-md-6">
                    <label class="form-label">联系方式文字</label>
                    <input type="text" class="form-control" name="promotion_contact_text" placeholder="唯一售后微信：">
                </div>
                <div class="col-md-6">
                    <label class="form-label">联系方式值</label>
                    <input type="text" class="form-control" name="promotion_contact_value" placeholder="hzoedu888">
                </div>

                <!-- 弹窗提示设置 -->
                <div class="col-12 mt-4">
                    <h5 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-comment-dots me-2"></i>弹窗提示设置
                    </h5>
                </div>
                <div class="col-12">
                    <label class="form-label">兑换成功提示</label>
                    <textarea class="form-control" name="exchange_success_message" rows="3" placeholder="兑换成功！请查看下方内容"></textarea>
                    <div class="form-text">兑换成功时显示的提示信息</div>
                </div>
                <div class="col-12">
                    <label class="form-label">兑换失败提示</label>
                    <textarea class="form-control" name="exchange_error_message" rows="3" placeholder="兑换失败，请检查卡密是否正确"></textarea>
                    <div class="form-text">兑换失败时显示的提示信息</div>
                </div>
                <div class="col-12">
                    <label class="form-label">页面底部说明</label>
                    <textarea class="form-control" name="page_footer_notice" rows="4" placeholder="此为电子资料兑换下载系统，兑换后不支持任何理由的退换货"></textarea>
                    <div class="form-text">显示在页面底部的说明文字</div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// 保存前端设置
function saveFrontendSettings() {
    const form = document.getElementById("frontendForm");
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // 处理复选框
    const checkboxes = form.querySelectorAll("input[type=checkbox]");
    checkboxes.forEach(checkbox => {
        data[checkbox.name] = checkbox.checked ? "1" : "0";
    });
    
    console.log("保存前端设置:", data);
    AdminUtils.showMessage("前端设置保存成功", "success");
}
</script>
';

include 'layout.php';
