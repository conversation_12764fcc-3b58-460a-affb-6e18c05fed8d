<?php
$page_title = '前端设置';
$current_page = 'frontend-settings';

// 设置默认值
$frontendSettings = $frontendSettings ?? [
    'site_title' => '',
    'site_description' => '',
    'promotion_enabled' => 0,
    'promotion_title' => '',
    'promotion_btn1_text' => '',
    'promotion_btn1_url' => '',
    'promotion_btn2_text' => '',
    'promotion_btn2_url' => '',
    'promotion_btn3_text' => '',
    'promotion_btn3_url' => '',
    'promotion_contact_text' => '',
    'promotion_contact_value' => '',
    'exchange_success_message' => '',
    'exchange_error_message' => '',
    'page_footer_notice' => '',
    // 兑换确认对话框
    'exchange_confirm_title' => '兑换确认',
    'exchange_confirm_content' => '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
    // 验证提示
    'empty_card_message' => '请输入卡密',
    // 兑换错误提示
    'card_not_exist_message' => '卡密不存在或已失效',
    'card_already_used_message' => '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
    'card_disabled_message' => '卡密已被禁用，请联系微信hzoedu888',
    'card_expired_message' => '卡密已过期',
    // 查询错误提示
    'query_disabled_message' => '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
    'card_not_exchanged_message' => '该卡密尚未兑换，请先兑换后再查看兑换记录',
    'no_exchange_record_message' => '未找到兑换记录',
    // 成功提示
    'query_success_message' => '查询成功',
    // 复制功能提示
    'copy_success_message' => '✅ 链接复制成功！',
    'copy_error_message' => '❌ 复制失败，请手动复制',
    // 网络错误提示
    'network_error_message' => '网络错误，请稍后重试',
];

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">前端设置</h2>
        <p class="text-muted mb-0">配置前端页面的显示内容和样式</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveFrontendSettings()">
            <i class="fas fa-save me-2"></i>保存设置
        </button>
    </div>
</div>

<!-- 前端设置标签页 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="frontendSettingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                <i class="fas fa-globe me-2"></i>基本信息
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="promotion-tab" data-bs-toggle="tab" data-bs-target="#promotion" type="button" role="tab">
                <i class="fas fa-bullhorn me-2"></i>推广模块
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="popup-tab" data-bs-toggle="tab" data-bs-target="#popup" type="button" role="tab">
                <i class="fas fa-comment-dots me-2"></i>弹窗提示
            </button>
        </li>
    </ul>

    <div class="tab-content" id="frontendSettingsTabContent">
        <!-- 基本信息 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
            <div class="p-4">
                <form id="frontendForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="row align-items-center g-2">
                                <div class="col-auto">
                                    <label class="form-label mb-0">网站Logo</label>
                                </div>
                                <div class="col-sm-6">
                                    <input type="file" class="form-control" name="site_logo" accept="image/*">
                                </div>
                                <div class="col">
                                    <small class="text-muted">建议尺寸：200x60像素</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row align-items-center g-2">
                                <div class="col-auto">
                                    <label class="form-label mb-0">网站标题</label>
                                </div>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" name="site_title" value="' . htmlspecialchars($frontendSettings['site_title']) . '" placeholder="请输入网站标题">
                                </div>
                                <div class="col">
                                    <small class="text-muted">显示在浏览器标题栏</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row align-items-start g-2">
                                <div class="col-auto">
                                    <label class="form-label mb-0 pt-2">网站描述</label>
                                </div>
                                <div class="col-sm-6">
                                    <textarea class="form-control" name="site_description" rows="3" placeholder="请输入网站描述">' . htmlspecialchars($frontendSettings['site_description']) . '</textarea>
                                </div>
                                <div class="col">
                                    <small class="text-muted pt-2 d-block">显示在页面头部的描述信息</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 推广模块 -->
        <div class="tab-pane fade" id="promotion" role="tabpanel">
            <div class="p-4">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">推广模块</label>
                            </div>
                            <div class="col-5">
                                <div class="form-check form-switch d-flex align-items-center" style="min-height: 38px;">
                                    <input class="form-check-input" type="checkbox" name="promotion_enabled" id="promotionEnabled" value="1" ' . ($frontendSettings['promotion_enabled'] ? 'checked' : '') . '>
                                    <label class="form-check-label ms-2" for="promotionEnabled">启用推广模块</label>
                                </div>
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">兑换成功后显示推广内容</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">推广标题</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_title" value="' . htmlspecialchars($frontendSettings['promotion_title']) . '" placeholder="您还可以点击以下按钮获取更多免费资源">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">推广模块的标题文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮1文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn1_text" value="' . htmlspecialchars($frontendSettings['promotion_btn1_text']) . '" placeholder="电子资料包">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第一个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮1链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn1_url" value="' . htmlspecialchars($frontendSettings['promotion_btn1_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第一个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮2文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn2_text" value="' . htmlspecialchars($frontendSettings['promotion_btn2_text']) . '" placeholder="免费网课">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第二个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮2链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn2_url" value="' . htmlspecialchars($frontendSettings['promotion_btn2_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第二个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮3文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn3_text" value="' . htmlspecialchars($frontendSettings['promotion_btn3_text']) . '" placeholder="官方网站">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第三个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮3链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn3_url" value="' . htmlspecialchars($frontendSettings['promotion_btn3_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第三个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">联系方式文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_contact_text" value="' . htmlspecialchars($frontendSettings['promotion_contact_text']) . '" placeholder="唯一售后微信：">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">联系方式的描述文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">联系方式值</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_contact_value" value="' . htmlspecialchars($frontendSettings['promotion_contact_value']) . '" placeholder="hzoedu888">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">具体的联系方式（微信号等）</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 弹窗提示 -->
        <div class="tab-pane fade" id="popup" role="tabpanel">
            <div class="p-4">
                <div class="row g-3">
                    <!-- 兑换确认对话框 -->
                    <div class="col-12">
                        <h6 class="text-primary mb-3">🔄 兑换确认对话框</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">确认标题</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="exchange_confirm_title" value="' . htmlspecialchars($frontendSettings['exchange_confirm_title'] ?? '兑换确认') . '" placeholder="兑换确认">
                            </div>
                            <div class="col">
                                <small class="text-muted">兑换确认对话框的标题</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">确认内容</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="exchange_confirm_content" rows="3" placeholder="此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！">' . htmlspecialchars($frontendSettings['exchange_confirm_content'] ?? '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">兑换确认对话框的内容</small>
                            </div>
                        </div>
                    </div>

                    <!-- 验证提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">⚠️ 验证提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">空卡密提示</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="empty_card_message" value="' . htmlspecialchars($frontendSettings['empty_card_message'] ?? '请输入卡密') . '" placeholder="请输入卡密">
                            </div>
                            <div class="col">
                                <small class="text-muted">用户未输入卡密时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 兑换错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">❌ 兑换错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密不存在</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_not_exist_message" value="' . htmlspecialchars($frontendSettings['card_not_exist_message'] ?? '卡密不存在或已失效') . '" placeholder="卡密不存在或已失效">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密不存在时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">已兑换提示</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="card_already_used_message" rows="2" placeholder="您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！">' . htmlspecialchars($frontendSettings['card_already_used_message'] ?? '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">卡密已兑换时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密被禁用</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_disabled_message" value="' . htmlspecialchars($frontendSettings['card_disabled_message'] ?? '卡密已被禁用，请联系微信hzoedu888') . '" placeholder="卡密已被禁用，请联系微信hzoedu888">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密被禁用时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密过期</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_expired_message" value="' . htmlspecialchars($frontendSettings['card_expired_message'] ?? '卡密已过期') . '" placeholder="卡密已过期">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密过期时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 查询错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">🔍 查询错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">查询禁用卡密</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="query_disabled_message" rows="2" placeholder="卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888">' . htmlspecialchars($frontendSettings['query_disabled_message'] ?? '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">查询被禁用卡密时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">未兑换提示</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="card_not_exchanged_message" rows="2" placeholder="该卡密尚未兑换，请先兑换后再查看兑换记录">' . htmlspecialchars($frontendSettings['card_not_exchanged_message'] ?? '该卡密尚未兑换，请先兑换后再查看兑换记录') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">查询未兑换卡密时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">无兑换记录</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="no_exchange_record_message" value="' . htmlspecialchars($frontendSettings['no_exchange_record_message'] ?? '未找到兑换记录') . '" placeholder="未找到兑换记录">
                            </div>
                            <div class="col">
                                <small class="text-muted">未找到兑换记录时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 成功提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">✅ 成功提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">兑换成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="exchange_success_message" value="' . htmlspecialchars($frontendSettings['exchange_success_message'] ?? '兑换成功！') . '" placeholder="兑换成功！">
                            </div>
                            <div class="col">
                                <small class="text-muted">兑换成功时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">查询成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="query_success_message" value="' . htmlspecialchars($frontendSettings['query_success_message'] ?? '查询成功') . '" placeholder="查询成功">
                            </div>
                            <div class="col">
                                <small class="text-muted">查询成功时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 复制功能提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">📋 复制功能提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">复制成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="copy_success_message" value="' . htmlspecialchars($frontendSettings['copy_success_message'] ?? '✅ 链接复制成功！') . '" placeholder="✅ 链接复制成功！">
                            </div>
                            <div class="col">
                                <small class="text-muted">复制成功时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">复制失败</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="copy_error_message" value="' . htmlspecialchars($frontendSettings['copy_error_message'] ?? '❌ 复制失败，请手动复制') . '" placeholder="❌ 复制失败，请手动复制">
                            </div>
                            <div class="col">
                                <small class="text-muted">复制失败时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 网络错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">🌐 网络错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">网络错误</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="network_error_message" value="' . htmlspecialchars($frontendSettings['network_error_message'] ?? '网络错误，请稍后重试') . '" placeholder="网络错误，请稍后重试">
                            </div>
                            <div class="col">
                                <small class="text-muted">网络错误时的提示</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
';

?>

<script>
// 保存前端设置
function saveFrontendSettings() {
    const formData = new FormData();

    // 收集基本信息表单数据
    const form = document.getElementById("frontendForm");
    const formElements = form.querySelectorAll("input, select, textarea");
    formElements.forEach(element => {
        if (element.type === 'checkbox') {
            formData.set(element.name, element.checked ? "1" : "0");
        } else {
            formData.set(element.name, element.value);
        }
    });

    // 收集推广模块数据
    const promotionInputs = document.querySelectorAll("#promotion input, #promotion textarea");
    promotionInputs.forEach(element => {
        if (element.type === 'checkbox') {
            formData.set(element.name, element.checked ? "1" : "0");
        } else {
            formData.set(element.name, element.value);
        }
    });

    // 收集弹窗提示数据
    const popupInputs = document.querySelectorAll("#popup input, #popup textarea");
    popupInputs.forEach(element => {
        formData.set(element.name, element.value);
    });

    // 发送AJAX请求
    fetch('/admin/saveFrontendSettings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            AdminUtils.showMessage(data.msg, "success");
        } else {
            AdminUtils.showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        AdminUtils.showMessage("保存失败，请重试", "error");
    });
}
</script>

<?php
include 'layout.php';
?>
