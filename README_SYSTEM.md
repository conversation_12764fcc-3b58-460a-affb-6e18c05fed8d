# 卡密兑换系统

基于 ThinkPHP 8.1.2 开发的电子资料卡密兑换系统。

## 功能特性

### 用户前端
- 卡密兑换功能
- 兑换记录查询
- 响应式设计
- 文件下载

### 管理后端
- 控制台统计
- 卡密管理
- 分类管理
- 内容管理
- 系统设置

## 安装部署

### 1. 环境要求
- PHP >= 8.0
- MySQL >= 5.7
- Apache/Nginx

### 2. 数据库配置
1. 创建数据库 `731kmxt`
2. 修改 `config/database.php` 中的数据库配置
3. 运行数据库初始化命令：
```bash
php think init:database
```

### 3. 目录权限
确保以下目录可写：
- `runtime/`
- `public/uploads/`

### 4. 访问地址
- 用户前端：`http://your-domain/`
- 管理后端：`http://your-domain/admin/login`

## 默认账号

### 管理员账号
- 用户名：`admin`
- 密码：`123456`

### 测试卡密
- `XXXX-XXXX-XXXX-1234` (网络安全)
- `XXXX-XXXX-XXXX-5678` (月度会员)
- `XXXX-XXXX-XXXX-9012` (编程开发)

## 目录结构

```
├── app/                    # 应用目录
│   ├── controller/         # 控制器
│   ├── model/             # 模型
│   └── command/           # 命令行
├── config/                # 配置文件
├── database/              # 数据库文件
├── public/                # 公共目录
│   └── static/            # 静态资源
├── view/                  # 视图文件
│   ├── admin/             # 管理后端视图
│   ├── index/             # 用户前端视图
│   └── error/             # 错误页面
└── route/                 # 路由配置
```

## 主要文件说明

### 控制器
- `app/controller/Index.php` - 用户前端控制器
- `app/controller/Admin.php` - 管理后端控制器
- `app/controller/Download.php` - 下载控制器

### 模型
- `app/model/Card.php` - 卡密模型
- `app/model/Category.php` - 分类模型
- `app/model/Content.php` - 内容模型
- `app/model/ExchangeRecord.php` - 兑换记录模型
- `app/model/AdminUser.php` - 管理员模型

### 视图
- `view/index/index.html` - 用户兑换页面
- `view/admin/layout.html` - 管理后端布局
- `view/admin/dashboard.html` - 控制台页面
- `view/admin/login.html` - 登录页面

## 数据库表结构

### 主要数据表
- `cards` - 卡密表
- `categories` - 分类表
- `contents` - 内容表
- `exchange_records` - 兑换记录表
- `admin_users` - 管理员表

## 安全特性

- 防止频繁请求
- IP地址记录
- 兑换记录追踪
- 下载权限验证
- 管理员登录验证

## 技术栈

- **后端框架**：ThinkPHP 8.1.2
- **前端框架**：Bootstrap 5
- **数据库**：MySQL
- **图表库**：Chart.js
- **图标库**：Bootstrap Icons

## 注意事项

1. 请确保 `public/uploads/` 目录存在且可写
2. 生产环境请修改默认管理员密码
3. 建议配置 HTTPS 以保证数据传输安全
4. 定期备份数据库数据

## 联系方式

如有问题请联系：hzoedu888
