<?php

namespace app\controller;

use app\BaseController;
use app\model\AdminUser;
use think\facade\Session;
use think\facade\View;

class Admin extends BaseController
{
    /**
     * 管理员信息
     */
    protected $admin = null;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();

        // 只有非登录页面才检查登录状态
        $action = $this->request->action();
        if ($action !== 'login') {
            $this->checkLogin();
        }
    }

    /**
     * 检查登录状态
     */
    protected function checkLogin()
    {
        $adminId = Session::get('admin_id');

        if (!$adminId) {
            // 如果是AJAX请求
            if ($this->request->isAjax()) {
                throw new \think\exception\HttpResponseException(json(['code' => 401, 'msg' => '请先登录']));
            }
            // 重定向到登录页
            throw new \think\exception\HttpResponseException(redirect('/admin/login'));
        }

        // 获取管理员信息
        $this->admin = AdminUser::find($adminId);
        if (!$this->admin || $this->admin->status != AdminUser::STATUS_ENABLED) {
            Session::delete('admin_id');
            if ($this->request->isAjax()) {
                throw new \think\exception\HttpResponseException(json(['code' => 401, 'msg' => '账号已被禁用']));
            }
            throw new \think\exception\HttpResponseException(redirect('/admin/login'));
        }

        // 传递管理员信息到视图
        View::assign('admin', $this->admin);
    }

    /**
     * 登录页面
     */
    public function login()
    {
        try {
            // 如果已登录，跳转到控制台
            if (Session::get('admin_id')) {
                if ($this->request->isPost()) {
                    return json(['code' => 1, 'msg' => '已登录', 'url' => '/admin/dashboard']);
                }
                return redirect('/admin/dashboard');
            }

            if ($this->request->isPost()) {
                $username = $this->request->post('username', '');
                $password = $this->request->post('password', '');

                if (empty($username) || empty($password)) {
                    return json(['code' => 0, 'msg' => '用户名和密码不能为空']);
                }

                // 查找管理员
                $admin = AdminUser::findByUsername($username);
                if (!$admin) {
                    return json(['code' => 0, 'msg' => '用户名或密码错误']);
                }

                // 验证密码
                if (!$admin->checkPassword($password)) {
                    return json(['code' => 0, 'msg' => '用户名或密码错误']);
                }

                // 更新登录信息
                $admin->updateLoginInfo();

                // 设置会话
                Session::set('admin_id', $admin->id);

                return json(['code' => 1, 'msg' => '登录成功', 'url' => '/admin/dashboard']);
            }

            return View::fetch('admin/simple_login');
        } catch (\Exception $e) {
            if ($this->request->isPost()) {
                return json(['code' => 0, 'msg' => 'Error: ' . $e->getMessage()]);
            }
            return 'Error: ' . $e->getMessage() . '<br>File: ' . $e->getFile() . '<br>Line: ' . $e->getLine();
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        Session::delete('admin_id');
        return redirect('/admin/login');
    }

    /**
     * 控制台首页
     */
    public function dashboard()
    {
        try {
            // 获取真实统计数据
            $stats = $this->getDashboardStats();

            // 传递当前页面标识和统计数据
            View::assign('current_page', 'dashboard');
            View::assign('page_title', '控制台');
            View::assign('stats', $stats);

            return View::fetch('admin/dashboard');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取控制台统计数据
     */
    private function getDashboardStats()
    {
        // 卡密统计
        $totalCards = \app\model\Card::count();
        $usedCards = \app\model\Card::where('status', \app\model\Card::STATUS_USED)->count();
        $unusedCards = \app\model\Card::where('status', \app\model\Card::STATUS_UNUSED)->count();
        $disabledCards = \app\model\Card::where('status', \app\model\Card::STATUS_DISABLED)->count();

        // 分类统计
        $totalCategories = \app\model\Category::count();

        // 内容统计
        $totalContents = \app\model\Content::count();

        // 兑换记录统计
        $totalExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)->count();

        // 今日兑换统计
        $todayExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->whereTime('exchange_time', 'today')
            ->count();

        // 本月兑换统计
        $monthExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->whereTime('exchange_time', 'month')
            ->count();

        // 最近活动记录
        $recentActivities = $this->getRecentActivities();

        return [
            'total_cards' => $totalCards,
            'used_cards' => $usedCards,
            'unused_cards' => $unusedCards,
            'disabled_cards' => $disabledCards,
            'total_categories' => $totalCategories,
            'total_contents' => $totalContents,
            'total_exchanges' => $totalExchanges,
            'today_exchanges' => $todayExchanges,
            'month_exchanges' => $monthExchanges,
            'recent_activities' => $recentActivities
        ];
    }

    /**
     * 获取使用趋势数据
     */
    public function getUsageTrend()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $period = $this->request->post('period', 7);
            $period = intval($period);

            // 根据时间段获取数据
            $data = $this->getUsageTrendData($period);

            return json(['code' => 1, 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取使用趋势数据
     */
    private function getUsageTrendData($period)
    {
        $labels = [];
        $data = [];

        for ($i = $period - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $labels[] = date('m-d', strtotime($date));

            // 获取当天的兑换次数
            $count = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
                ->whereTime('exchange_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])
                ->count();

            $data[] = $count;
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * 获取最近活动记录 - 每个卡密只显示一条记录
     */
    private function getRecentActivities()
    {
        $activities = [];
        $cardNumbers = []; // 用于记录已处理的卡密号码

        // 获取最近的卡密记录（按更新时间排序，确保获取最新状态）
        $recentCards = \app\model\Card::with(['category'])
            ->order('update_time', 'desc')
            ->limit(20)
            ->select();

        foreach ($recentCards as $card) {
            // 如果这个卡密号码已经处理过，跳过
            if (in_array($card['card_number'], $cardNumbers)) {
                continue;
            }

            // 检查卡密是否仍然存在（用于检测删除状态）
            $cardExists = \app\model\Card::where('card_number', $card['card_number'])->find();

            // 确定操作类型和时间
            $action = $this->getCardLatestAction($card);
            $actionTime = $this->getCardLatestActionTime($card);

            $activity = [
                'type' => 'card_activity',
                'card_id' => $card['id'],
                'card_number' => $card['card_number'],
                'category_name' => isset($card['category']) ? $card['category']['name'] : '未分类',
                'time' => $actionTime,
                'status' => $card['status'],
                'action' => $action,
                'action_time' => $actionTime,
                'is_deleted' => !$cardExists,
                'use_status' => $this->getUseStatus($card, !$cardExists),
                'enable_status' => $this->getEnableStatus($card['status'], !$cardExists)
            ];
            $activities[] = $activity;
            $cardNumbers[] = $card['card_number']; // 记录已处理的卡密
        }

        // 获取最近的兑换记录，但只处理未在卡密记录中出现的卡密
        $recentExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->order('exchange_time', 'desc')
            ->limit(15)
            ->select();

        foreach ($recentExchanges as $exchange) {
            // 如果这个卡密号码已经处理过，跳过
            if (in_array($exchange['card_number'], $cardNumbers)) {
                continue;
            }

            // 检查对应的卡密是否存在
            $card = \app\model\Card::where('card_number', $exchange['card_number'])->find();

            $activity = [
                'type' => 'exchange',
                'card_id' => $card ? $card['id'] : null,
                'card_number' => $exchange['card_number'],
                'category_name' => '兑换记录',
                'time' => $exchange['exchange_time'],
                'status' => 2, // 已使用
                'action' => '兑换成功',
                'action_time' => $exchange['exchange_time'],
                'is_deleted' => !$card,
                'use_status' => $card ? $this->getUseStatus($card, !$card) : '已使用',
                'enable_status' => $card ? $this->getEnableStatus($card['status'], !$card) : '已删除'
            ];
            $activities[] = $activity;
            $cardNumbers[] = $exchange['card_number']; // 记录已处理的卡密
        }

        // 按时间排序并限制数量
        usort($activities, function($a, $b) {
            return strtotime($b['action_time']) - strtotime($a['action_time']);
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * 获取卡密的最新操作类型
     */
    private function getCardLatestAction($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return '卡密使用';
            case \app\model\Card::STATUS_DISABLED:
                return '卡密禁用';
            case \app\model\Card::STATUS_UNUSED:
            default:
                return '新增卡密';
        }
    }

    /**
     * 获取卡密的最新操作时间
     */
    private function getCardLatestActionTime($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return $card['used_time'] ?: $card['update_time'];
            case \app\model\Card::STATUS_DISABLED:
            case \app\model\Card::STATUS_UNUSED:
            default:
                return $card['update_time'] ?: $card['create_time'];
        }
    }

    /**
     * 获取使用状态文本 - 只显示已使用和未使用，基于是否有使用时间判断
     */
    private function getUseStatus($card, $isDeleted = false)
    {
        // 使用状态只看是否有使用时间，不受禁用/启用影响
        if (isset($card['used_time']) && !empty($card['used_time'])) {
            return '已使用';
        } else {
            return '未使用';
        }
    }

    /**
     * 获取启用状态文本 - 显示已禁用、已启用、已删除
     */
    private function getEnableStatus($status, $isDeleted = false)
    {
        // 如果已删除，优先显示已删除状态
        if ($isDeleted) {
            return '已删除';
        }

        switch ($status) {
            case \app\model\Card::STATUS_DISABLED:
                return '已禁用';
            case \app\model\Card::STATUS_UNUSED:
            case \app\model\Card::STATUS_USED:
                return '已启用';
            default:
                return '已启用';
        }
    }

    /**
     * 获取卡密详情
     */
    public function getCardDetail()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $cardId = $this->request->post('card_id');
            if (empty($cardId)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::with(['category'])->find($cardId);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在或已被删除']);
            }

            // 获取关联的内容信息
            $contentIds = json_decode($card['content_ids'], true);
            $contents = [];
            if (!empty($contentIds)) {
                $contents = \app\model\Content::whereIn('id', $contentIds)->select();
            }

            // 获取兑换记录
            $exchangeRecords = \app\model\ExchangeRecord::where('card_number', $card['card_number'])
                ->order('exchange_time', 'desc')
                ->select();

            $cardDetail = [
                'id' => $card['id'],
                'card_number' => $card['card_number'],
                'card_type' => $card['card_type'],
                'batch_id' => $card['batch_id'],
                'category' => $card['category'] ? $card['category']['name'] : '未分类',
                'value' => $card['value'],
                'valid_days' => $card['valid_days'],
                'status' => $card['status'],
                'status_text' => $this->getCardStatusText($card['status']),
                'used_time' => $card['used_time'],
                'used_ip' => $card['used_ip'],
                'expire_time' => $card['expire_time'],
                'create_time' => $card['create_time'],
                'update_time' => $card['update_time'],
                'remark' => $card['remark'],
                'contents' => $contents,
                'exchange_records' => $exchangeRecords
            ];

            return json(['code' => 1, 'data' => $cardDetail]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取详情失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密状态文本
     */
    private function getCardStatusText($status)
    {
        switch ($status) {
            case \app\model\Card::STATUS_UNUSED:
                return '未使用';
            case \app\model\Card::STATUS_USED:
                return '已使用';
            case \app\model\Card::STATUS_DISABLED:
                return '已禁用';
            default:
                return '未知状态';
        }
    }

    /**
     * 获取卡密操作类型
     */
    private function getCardAction($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_UNUSED:
                return '新增卡密';
            case \app\model\Card::STATUS_USED:
                return '卡密使用';
            case \app\model\Card::STATUS_DISABLED:
                return '卡密禁用';
            default:
                return '未知操作';
        }
    }

    /**
     * 获取卡密操作时间
     */
    private function getCardActionTime($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return $card['used_time'] ?: $card['update_time'];
            default:
                return $card['update_time'];
        }
    }

    /**
     * 获取统计数据
     */
    public function getStats()
    {
        try {
            $db = \think\facade\Db::connect();

            // 获取基本统计
            $totalCards = $db->table('cards')->count();
            $usedCards = $db->table('cards')->where('status', 2)->count();
            $unusedCards = $db->table('cards')->where('status', 1)->count();
            $todayExchange = $db->table('exchange_records')
                               ->whereTime('exchange_time', 'today')
                               ->where('status', 1)
                               ->count();

            // 获取最近兑换记录
            $recentExchanges = $db->table('exchange_records')
                                 ->alias('er')
                                 ->join('cards c', 'er.card_id = c.id', 'left')
                                 ->field('er.card_number, c.card_type, er.exchange_time, er.status')
                                 ->order('er.exchange_time', 'desc')
                                 ->limit(5)
                                 ->select();

            $data = [
                'overview' => [
                    'total_cards' => $totalCards,
                    'used_cards' => $usedCards,
                    'unused_cards' => $unusedCards,
                    'today_exchange' => $todayExchange
                ],
                'recent_exchanges' => $recentExchanges
            ];

            return json(['code' => 1, 'data' => $data]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 卡密管理
     */
    public function cards()
    {
        try {
            // 获取筛选参数
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 15);
            $limit = in_array($pageSize, [10, 15, 20, 50, 100]) ? $pageSize : 15;

            // 构建查询条件
            $where = [];
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if ($search) {
                $where[] = ['card_number', 'like', '%' . $search . '%'];
            }

            // 获取卡密列表
            $cards = \app\model\Card::with(['category'])
                ->where($where)
                ->order('create_time', 'desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page,
                ]);

            // 获取关联的内容信息
            foreach ($cards as $card) {
                if ($card->content_ids) {
                    $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);
                    if ($contentIds) {
                        $contents = \app\model\Content::whereIn('id', $contentIds)->column('title');
                        $card->content_titles = $contents;
                    } else {
                        $card->content_titles = [];
                    }
                } else {
                    $card->content_titles = [];
                }
            }

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPaginationForCards($cards);

            View::assign('current_page', 'cards');
            View::assign('page_title', '卡密管理');
            View::assign('cards', $cards);
            View::assign('status', $status);
            View::assign('search', $search);
            View::assign('pagination_html', $paginationHtml);
            View::assign('search_params', [
                'status' => $status,
                'search' => $search,
                'page_size' => $limit
            ]);

            return View::fetch('admin/cards');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 切换卡密状态
     */
    public function toggleCardStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            $card->status = intval($status);
            $card->save();

            $statusText = $status == 1 ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "卡密{$statusText}成功"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除卡密
     */
    public function deleteCard()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 删除卡密
            $card->delete();

            return json(['code' => 1, 'msg' => '卡密删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量删除卡密
     */
    public function batchDeleteCards()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $ids = $this->request->post('ids');

            if (empty($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的卡密']);
            }

            // 确保ids是数组
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }

            // 过滤空值
            $ids = array_filter($ids);

            if (empty($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的卡密']);
            }

            $successCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $card = \app\model\Card::find($id);
                    if ($card) {
                        $card->delete();
                        $successCount++;
                    } else {
                        $errors[] = "卡密ID {$id} 不存在";
                    }
                } catch (\Exception $e) {
                    $errors[] = "删除卡密ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                $message = "成功删除 {$successCount} 个卡密";
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个失败";
                }
                return json(['code' => 1, 'msg' => $message, 'data' => [
                    'success_count' => $successCount,
                    'errors' => $errors
                ]]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有卡密被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密详情
     */
    public function getCardDetails()
    {
        try {
            $id = $this->request->get('id');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 获取关联的内容标题
            $contentTitles = [];
            if ($card->content_ids) {
                $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);
                if ($contentIds && is_array($contentIds)) {
                    $contents = \app\model\Content::whereIn('id', $contentIds)->column('title');
                    $contentTitles = array_values($contents);
                }
            }

            // 准备返回数据
            $cardData = $card->toArray();
            $cardData['content_titles'] = $contentTitles;

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $cardData
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取分类下的内容
     */
    public function getCategoryContents()
    {
        try {
            $categoryId = $this->request->get('category_id');

            if (empty($categoryId)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $contents = \app\model\Content::where('category_id', $categoryId)
                ->where('status', 1)
                ->field('id,title')
                ->order('sort_order', 'asc')
                ->order('create_time', 'desc')
                ->select();

            return json(['code' => 1, 'data' => $contents]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密设置
     */
    public function getCardSettings()
    {
        try {
            // 从数据库获取设置
            $settings = $this->getCardSettingsData();
            return json(['code' => 1, 'data' => $settings]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取前端设置
     */
    public function getFrontendSettings()
    {
        try {
            // 从数据库获取前端设置
            $settings = $this->getFrontendSettingsData();
            return json(['code' => 1, 'data' => $settings]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取前端设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存系统设置
     */
    public function saveSettings()
    {
        try {
            $data = $this->request->post();

            if (empty($data)) {
                return json(['code' => 0, 'msg' => '没有接收到设置数据']);
            }

            // 验证数据
            $validatedData = $this->validateSettingsData($data);

            // 保存到数据库
            $this->saveSettingsToDatabase($validatedData);

            return json(['code' => 1, 'msg' => '设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 验证设置数据
     */
    private function validateSettingsData($data)
    {
        $validated = [];

        // 卡密设置验证
        if (isset($data['card_generate_count'])) {
            $validated['card_generate_count'] = max(1, min(1000, intval($data['card_generate_count'])));
        }
        if (isset($data['card_length'])) {
            $validated['card_length'] = max(4, min(32, intval($data['card_length'])));
        }
        if (isset($data['card_character_type'])) {
            $allowedTypes = ['mixed', 'numbers', 'letters', 'uppercase', 'lowercase', 'alphanumeric'];
            $validated['card_character_type'] = in_array($data['card_character_type'], $allowedTypes) ? $data['card_character_type'] : 'mixed';
        }
        if (isset($data['card_usage_limit'])) {
            $validated['card_usage_limit'] = max(1, min(999, intval($data['card_usage_limit'])));
        }
        if (isset($data['card_prefix'])) {
            $validated['card_prefix'] = substr($data['card_prefix'], 0, 10);
        }
        if (isset($data['card_suffix'])) {
            $validated['card_suffix'] = substr($data['card_suffix'], 0, 10);
        }
        if (isset($data['card_validity_days'])) {
            $validated['card_validity_days'] = max(0, min(3650, intval($data['card_validity_days'])));
        }
        if (isset($data['card_separator'])) {
            $allowedSeparators = ['', '-', '_', '.'];
            $validated['card_separator'] = in_array($data['card_separator'], $allowedSeparators) ? $data['card_separator'] : '';
        }
        if (isset($data['card_case_sensitive'])) {
            $validated['card_case_sensitive'] = $data['card_case_sensitive'] === 'on' ? 1 : 0;
        }
        if (isset($data['card_auto_delete'])) {
            $validated['card_auto_delete'] = $data['card_auto_delete'] === 'on' ? 1 : 0;
        }
        if (isset($data['card_log_usage'])) {
            $validated['card_log_usage'] = $data['card_log_usage'] === 'on' ? 1 : 0;
        }
        if (isset($data['card_description'])) {
            $validated['card_description'] = $data['card_description'];
        }
        if (isset($data['card_success_title'])) {
            $validated['card_success_title'] = $data['card_success_title'];
        }
        if (isset($data['card_success_content'])) {
            $validated['card_success_content'] = $data['card_success_content'];
        }

        // 基本设置验证
        if (isset($data['site_name'])) {
            $validated['site_name'] = $data['site_name'];
        }
        if (isset($data['site_url'])) {
            $validated['site_url'] = $data['site_url'];
        }
        if (isset($data['site_description'])) {
            $validated['site_description'] = $data['site_description'];
        }
        if (isset($data['site_keywords'])) {
            $validated['site_keywords'] = $data['site_keywords'];
        }

        // 联系方式验证
        if (isset($data['contact_email'])) {
            $validated['contact_email'] = $data['contact_email'];
        }
        if (isset($data['contact_phone'])) {
            $validated['contact_phone'] = $data['contact_phone'];
        }
        if (isset($data['contact_qq'])) {
            $validated['contact_qq'] = $data['contact_qq'];
        }
        if (isset($data['contact_wechat'])) {
            $validated['contact_wechat'] = $data['contact_wechat'];
        }

        // 前端设置验证 - 确保所有字段都有值，即使表单中没有提交
        $frontendFields = [
            'site_logo' => '',
            'site_title' => '',
            'promotion_title' => '',
            'promotion_btn1_text' => '',
            'promotion_btn1_url' => '',
            'promotion_btn2_text' => '',
            'promotion_btn2_url' => '',
            'promotion_btn3_text' => '',
            'promotion_btn3_url' => '',
            'promotion_contact_text' => '',
            'promotion_contact_value' => '',
            'exchange_success_message' => '',
            'exchange_error_message' => '',
            'page_footer_notice' => ''
        ];

        foreach ($frontendFields as $field => $defaultValue) {
            if ($field === 'site_logo') {
                // 文件上传字段特殊处理，如果包含 [object File] 则跳过
                if (isset($data[$field]) && !empty($data[$field]) && strpos($data[$field], '[object File]') === false) {
                    $validated[$field] = $data[$field];
                }
                // 如果是文件对象字符串或空值，则不更新数据库中的值
            } else {
                $validated[$field] = isset($data[$field]) ? $data[$field] : $defaultValue;
            }
        }

        // 复选框字段需要特殊处理，未选中时不会在POST数据中出现
        $validated['promotion_enabled'] = isset($data['promotion_enabled']) && $data['promotion_enabled'] === '1' ? '1' : '0';

        return $validated;
    }

    /**
     * 保存设置到数据库
     */
    private function saveSettingsToDatabase($data)
    {
        $db = \think\facade\Db::connect();

        foreach ($data as $key => $value) {
            // 所有设置都保存到 settings 表
            $tableName = 'settings';

            // 检查设置是否已存在
            $existing = $db->table($tableName)->where('key', $key)->find();

            if ($existing) {
                // 更新现有设置
                $db->table($tableName)->where('key', $key)->update([
                    'value' => $value,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            } else {
                // 插入新设置
                $db->table($tableName)->insert([
                    'key' => $key,
                    'value' => $value,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }



    /**
     * 分类管理
     */
    public function categories()
    {
        try {
            // 获取所有分类数据（包括禁用的）
            $categories = \app\model\Category::getAllForAdmin();

            // 获取分类统计数据
            $stats = $this->getCategoryStats();

            View::assign('current_page', 'categories');
            View::assign('page_title', '分类管理');
            View::assign('categories', $categories);
            View::assign('stats', $stats);

            return View::fetch('admin/categories');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取分类统计数据
     */
    private function getCategoryStats()
    {
        try {
            // 总分类数
            $totalCategories = \app\model\Category::count();

            // 启用的分类数
            $enabledCategories = \app\model\Category::where('status', 1)->count();

            // 禁用的分类数
            $disabledCategories = \app\model\Category::where('status', 0)->count();

            // 一级分类数
            $level1Categories = \app\model\Category::where('parent_id', 0)->count();

            // 二级分类数
            $level2Categories = \app\model\Category::where('level', 2)->count();

            // 三级分类数
            $level3Categories = \app\model\Category::where('level', 3)->count();

            // 有内容的分类数
            $categoriesWithContent = \app\model\Category::whereExists(function($query) {
                $query->table('contents')->whereRaw('contents.category_id = categories.id');
            })->count();

            return [
                'total' => $totalCategories,
                'enabled' => $enabledCategories,
                'disabled' => $disabledCategories,
                'level1' => $level1Categories,
                'level2' => $level2Categories,
                'level3' => $level3Categories,
                'with_content' => $categoriesWithContent,
                'empty' => $totalCategories - $categoriesWithContent
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'level1' => 0,
                'level2' => 0,
                'level3' => 0,
                'with_content' => 0,
                'empty' => 0
            ];
        }
    }

    /**
     * 获取分类信息
     */
    public function getCategory()
    {
        try {
            if (!$this->request->isGet()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->get('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'id' => $category->id,
                    'parent_id' => $category->parent_id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'sort_order' => $category->sort_order,
                    'status' => $category->status
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取分类树（用于选择上级分类）
     */
    public function getCategoryTree()
    {
        try {
            $excludeId = $this->request->get('exclude_id', 0); // 排除的分类ID（编辑时排除自己和子级）

            // 获取所有启用的分类
            $categories = \app\model\Category::where('status', 1)
                                           ->order('sort_order', 'asc')
                                           ->order('id', 'asc')
                                           ->select()
                                           ->toArray();

            // 如果有排除ID，需要排除该分类及其所有子级
            if ($excludeId > 0) {
                $categories = $this->filterExcludedCategories($categories, $excludeId);
            }

            // 构建树形结构
            $tree = $this->buildCategoryTree($categories);

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 过滤排除的分类及其子级
     */
    private function filterExcludedCategories($categories, $excludeId)
    {
        $excludeIds = [$excludeId];

        // 递归找出所有子级分类
        $findChildren = function($parentId) use (&$findChildren, $categories, &$excludeIds) {
            foreach ($categories as $category) {
                if ($category['parent_id'] == $parentId) {
                    $excludeIds[] = $category['id'];
                    $findChildren($category['id']);
                }
            }
        };

        $findChildren($excludeId);

        // 过滤掉排除的分类
        return array_filter($categories, function($category) use ($excludeIds) {
            return !in_array($category['id'], $excludeIds);
        });
    }

    /**
     * 构建分类树
     */
    private function buildCategoryTree($categories, $parentId = 0, $level = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['level'] = $level;
                $category['children'] = $this->buildCategoryTree($categories, $category['id'], $level + 1);
                $tree[] = $category;
            }
        }
        return $tree;
    }

    /**
     * 添加/编辑分类
     */
    public function saveCategory()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必填字段
            if (empty($data['name'])) {
                return json(['code' => 0, 'msg' => '分类名称不能为空']);
            }

            // 处理数据
            $categoryData = [
                'name' => trim($data['name']),
                'description' => trim($data['description'] ?? ''),
                'sort_order' => intval($data['sort_order'] ?? 0),
                'status' => intval($data['status'] ?? 1),
                'parent_id' => intval($data['parent_id'] ?? 0),
            ];

            // 计算层级和路径
            if ($categoryData['parent_id'] > 0) {
                $parent = \app\model\Category::find($categoryData['parent_id']);
                if (!$parent) {
                    return json(['code' => 0, 'msg' => '父分类不存在']);
                }
                $categoryData['level'] = $parent->level + 1;
                $categoryData['path'] = $parent->path . ',' . ($data['id'] ?? 'NEW');
            } else {
                $categoryData['level'] = 1;
                $categoryData['path'] = ($data['id'] ?? 'NEW');
            }

            if (!empty($data['id'])) {
                // 编辑分类
                $category = \app\model\Category::find($data['id']);
                if (!$category) {
                    return json(['code' => 0, 'msg' => '分类不存在']);
                }

                // 更新路径
                if ($categoryData['parent_id'] > 0) {
                    $parent = \app\model\Category::find($categoryData['parent_id']);
                    $categoryData['path'] = $parent->path . ',' . $data['id'];
                } else {
                    $categoryData['path'] = $data['id'];
                }

                $category->save($categoryData);
                return json(['code' => 1, 'msg' => '分类更新成功']);
            } else {
                // 添加分类
                $category = \app\model\Category::create($categoryData);

                // 更新路径
                if ($categoryData['parent_id'] > 0) {
                    $parent = \app\model\Category::find($categoryData['parent_id']);
                    $category->path = $parent->path . ',' . $category->id;
                } else {
                    $category->path = $category->id;
                }
                $category->save();

                return json(['code' => 1, 'msg' => '分类添加成功']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除分类
     */
    public function deleteCategory()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            // 检查是否有子分类
            $children = \app\model\Category::where('parent_id', $id)->select();
            $hasChildren = count($children);

            if ($hasChildren > 0) {
                // 获取子分类名称用于错误提示
                $childNames = [];
                foreach ($children as $child) {
                    $childNames[] = $child->name;
                }
                return json([
                    'code' => 0,
                    'msg' => '该分类下还有 ' . $hasChildren . ' 个子分类，无法删除。子分类：' . implode('、', $childNames)
                ]);
            }

            // 检查是否有关联内容
            $contents = \app\model\Content::where('category_id', $id)->select();
            $hasContents = count($contents);

            if ($hasContents > 0) {
                return json([
                    'code' => 0,
                    'msg' => '该分类下还有 ' . $hasContents . ' 个内容，无法删除'
                ]);
            }

            $category->delete();
            return json(['code' => 1, 'msg' => '分类删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新分类状态
     */
    public function updateCategoryStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $newStatus = intval($status);
            $category->status = $newStatus;
            $category->save();

            // 级联操作子分类
            if ($newStatus == 0) {
                $this->disableChildCategories($id);
                $message = '分类及其所有子分类已禁用';
            } else {
                $this->enableChildCategories($id);
                $message = '分类及其所有子分类已启用';
            }

            return json(['code' => 1, 'msg' => $message]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 递归禁用所有子分类
     */
    private function disableChildCategories($parentId)
    {
        // 获取所有子分类
        $children = \app\model\Category::where('parent_id', $parentId)->select();

        foreach ($children as $child) {
            // 禁用子分类
            $child->status = 0;
            $child->save();

            // 递归禁用子分类的子分类
            $this->disableChildCategories($child->id);
        }
    }

    /**
     * 递归启用所有子分类
     */
    private function enableChildCategories($parentId)
    {
        // 获取所有子分类
        $children = \app\model\Category::where('parent_id', $parentId)->select();

        foreach ($children as $child) {
            // 启用子分类
            $child->status = 1;
            $child->save();

            // 递归启用子分类的子分类
            $this->enableChildCategories($child->id);
        }
    }

    /**
     * 更新分类排序
     */
    public function updateCategorySort()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $sortOrder = $this->request->post('sort_order');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            if (!is_numeric($sortOrder)) {
                return json(['code' => 0, 'msg' => '排序值必须是数字']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $oldSortOrder = $category->sort_order;
            $newSortOrder = intval($sortOrder);

            // 如果排序值没有变化，直接返回成功
            if ($oldSortOrder == $newSortOrder) {
                return json(['code' => 1, 'msg' => '排序值未变化']);
            }

            // 允许相同的排序值，不需要处理冲突
            // 同级分类可以有相同的排序值，系统会按照ID顺序作为次要排序

            $category->sort_order = $newSortOrder;
            $category->save();

            return json([
                'code' => 1,
                'msg' => '排序更新成功',
                'data' => [
                    'id' => $id,
                    'old_sort' => $oldSortOrder,
                    'new_sort' => $newSortOrder,
                    'parent_id' => $category->parent_id
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }



    /**
     * 批量删除分类
     */
    public function batchDeleteCategories()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (empty($data['ids']) || !is_array($data['ids'])) {
                return json(['code' => 0, 'msg' => '请选择要删除的分类']);
            }

            $ids = $data['ids'];
            $deletedCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $category = \app\model\Category::find($id);
                    if (!$category) {
                        $errors[] = "分类ID {$id} 不存在";
                        continue;
                    }

                    // 检查是否有子分类
                    $hasChildren = \app\model\Category::where('parent_id', $id)->count();
                    if ($hasChildren > 0) {
                        $errors[] = "分类 '{$category->name}' 下还有子分类，无法删除";
                        continue;
                    }

                    // 检查是否有关联内容
                    $hasContents = \app\model\Content::where('category_id', $id)->count();
                    if ($hasContents > 0) {
                        $errors[] = "分类 '{$category->name}' 下还有内容，无法删除";
                        continue;
                    }

                    $category->delete();
                    $deletedCount++;
                } catch (\Exception $e) {
                    $errors[] = "删除分类ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($deletedCount > 0) {
                $message = "成功删除 {$deletedCount} 个分类";
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个分类删除失败";
                }
                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => [
                        'deleted_count' => $deletedCount,
                        'errors' => $errors
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有分类被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 内容管理
     */
    public function contents()
    {
        try {
            // 获取筛选参数
            $categoryId = $this->request->get('category_id', '');
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 10); // 每页显示数量，默认10条

            // 构建查询条件
            $query = \app\model\Content::with(['category']);

            // 分类筛选（包含子分类）
            if (!empty($categoryId)) {
                // 获取该分类及其所有子分类的ID
                $categoryIds = \app\model\Category::getCategoryAndChildrenIds($categoryId);
                if (!empty($categoryIds)) {
                    $query->whereIn('category_id', $categoryIds);
                } else {
                    // 如果没有找到分类ID，则按原来的方式筛选
                    $query->where('category_id', $categoryId);
                }
            }

            // 状态筛选
            if ($status !== '') {
                $query->where('status', intval($status));
            }

            // 搜索筛选
            if (!empty($search)) {
                $query->where('title', 'like', '%' . $search . '%');
            }

            // 分页查询
            $contents = $query->order('sort_order', 'asc')
                             ->order('id', 'desc')
                             ->paginate([
                                 'list_rows' => $pageSize,
                                 'page' => $page,
                                 'query' => $this->request->get(),
                                 'path' => $this->request->baseUrl(),
                                 'fragment' => '',
                                 'var_page' => 'page'
                             ]);

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPagination($contents);

            // 为每个内容添加完整的分类路径
            foreach ($contents as $content) {
                if ($content['category']) {
                    $content['category_path'] = $this->getCategoryPath($content['category']);
                }
            }

            // 获取内容统计数据
            $stats = $this->getContentStats();

            View::assign('current_page', 'contents');
            View::assign('page_title', '内容管理');
            View::assign('contents', $contents);
            View::assign('stats', $stats);
            View::assign('search_params', [
                'category_id' => $categoryId,
                'status' => $status,
                'search' => $search,
                'page_size' => $pageSize
            ]);
            View::assign('pagination_html', $paginationHtml);

            return View::fetch('admin/contents');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取内容列表（AJAX）
     */
    public function getContentList()
    {
        try {
            // 获取筛选参数
            $categoryId = $this->request->get('category_id', '');
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 10);
            $limit = in_array($pageSize, [10, 20, 50, 100]) ? $pageSize : 10;

            // 构建查询条件
            $where = [];
            if (!empty($categoryId)) {
                $where[] = ['category_id', '=', $categoryId];
            }
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if (!empty($search)) {
                $where[] = ['title', 'like', '%' . $search . '%'];
            }

            // 获取内容列表
            $contents = \app\model\Content::with(['category'])
                                         ->where($where)
                                         ->order('id', 'desc')
                                         ->paginate([
                                             'list_rows' => $limit,
                                             'page' => $page,
                                             'fragment' => '',
                                             'var_page' => 'page'
                                         ]);

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPagination($contents);

            // 为每个内容添加完整的分类路径
            foreach ($contents as $content) {
                if ($content['category']) {
                    $content['category_path'] = $this->getCategoryPath($content['category']);
                } else {
                    $content['category_path'] = '未分类';
                }
            }

            // 获取内容统计数据
            $stats = $this->getContentStats();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'contents' => $contents->items(),
                    'pagination_html' => $paginationHtml,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }





    /**
     * 渲染自定义分页
     */
    private function renderCustomPagination($paginator)
    {
        try {
            // 安全获取分页信息
            $currentPage = 1;
            $lastPage = 1;
            $total = 0;

            // 尝试获取分页信息，如果失败则使用默认值
            if (is_object($paginator)) {
                try {
                    if (method_exists($paginator, 'currentPage')) {
                        $currentPage = $paginator->currentPage();
                    }
                    if (method_exists($paginator, 'lastPage')) {
                        $lastPage = $paginator->lastPage();
                    }
                    if (method_exists($paginator, 'total')) {
                        $total = $paginator->total();
                    }
                } catch (\Exception $e) {
                    // 如果获取分页信息失败，使用默认值
                }
            }

            // 如果只有一页或没有数据，显示简单分页
            if ($lastPage <= 1) {
                return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
            }

            // 构建URL
            $baseUrl = $this->request->baseUrl() . '/admin/contents';
            $queryParams = $this->request->get();

            $html = '<nav aria-label="分页导航">';
            $html .= '<ul class="custom-pagination">';

            // 上一页
            if ($currentPage > 1) {
                $queryParams['page'] = $currentPage - 1;
                $previousUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $previousUrl . '">上一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">上一页</span>';
                $html .= '</li>';
            }

            // 计算显示的页码范围
            $start = max(1, $currentPage - 2);
            $end = min($lastPage, $currentPage + 2);

            // 页码
            for ($i = $start; $i <= $end; $i++) {
                if ($i == $currentPage) {
                    $html .= '<li class="custom-page-item active">';
                    $html .= '<span class="custom-page-link active">' . $i . '</span>';
                    $html .= '</li>';
                } else {
                    $queryParams['page'] = $i;
                    $pageUrl = $baseUrl . '?' . http_build_query($queryParams);
                    $html .= '<li class="custom-page-item">';
                    $html .= '<a class="custom-page-link" href="' . $pageUrl . '">' . $i . '</a>';
                    $html .= '</li>';
                }
            }

            // 下一页
            if ($currentPage < $lastPage) {
                $queryParams['page'] = $currentPage + 1;
                $nextUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $nextUrl . '">下一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">下一页</span>';
                $html .= '</li>';
            }

            $html .= '</ul>';
            $html .= '</nav>';

            return $html;

        } catch (\Exception $e) {
            // 如果出现任何错误，返回默认分页
            return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
        }
    }

    /**
     * 渲染卡密页面的自定义分页
     */
    private function renderCustomPaginationForCards($paginator)
    {
        try {
            // 安全获取分页信息
            $currentPage = 1;
            $lastPage = 1;
            $total = 0;

            // 尝试获取分页信息，如果失败则使用默认值
            if (is_object($paginator)) {
                try {
                    if (method_exists($paginator, 'currentPage')) {
                        $currentPage = $paginator->currentPage();
                    }
                    if (method_exists($paginator, 'lastPage')) {
                        $lastPage = $paginator->lastPage();
                    }
                    if (method_exists($paginator, 'total')) {
                        $total = $paginator->total();
                    }
                } catch (\Exception $e) {
                    // 如果获取分页信息失败，使用默认值
                }
            }

            // 如果只有一页或没有数据，显示简单分页
            if ($lastPage <= 1) {
                return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
            }

            // 构建URL
            $baseUrl = $this->request->baseUrl() . '/admin/cards';
            $queryParams = $this->request->get();

            $html = '<nav aria-label="分页导航">';
            $html .= '<ul class="custom-pagination">';

            // 上一页
            if ($currentPage > 1) {
                $queryParams['page'] = $currentPage - 1;
                $previousUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $previousUrl . '">上一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">上一页</span>';
                $html .= '</li>';
            }

            // 计算显示的页码范围
            $start = max(1, $currentPage - 2);
            $end = min($lastPage, $currentPage + 2);

            // 页码
            for ($i = $start; $i <= $end; $i++) {
                if ($i == $currentPage) {
                    $html .= '<li class="custom-page-item active">';
                    $html .= '<span class="custom-page-link active">' . $i . '</span>';
                    $html .= '</li>';
                } else {
                    $queryParams['page'] = $i;
                    $pageUrl = $baseUrl . '?' . http_build_query($queryParams);
                    $html .= '<li class="custom-page-item">';
                    $html .= '<a class="custom-page-link" href="' . $pageUrl . '">' . $i . '</a>';
                    $html .= '</li>';
                }
            }

            // 下一页
            if ($currentPage < $lastPage) {
                $queryParams['page'] = $currentPage + 1;
                $nextUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $nextUrl . '">下一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">下一页</span>';
                $html .= '</li>';
            }

            $html .= '</ul>';
            $html .= '</nav>';

            return $html;

        } catch (\Exception $e) {
            // 如果出现任何错误，返回默认分页
            return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
        }
    }

    /**
     * 获取分类的完整路径
     */
    private function getCategoryPath($category)
    {
        $path = [];
        $current = $category;

        // 向上追溯到根分类
        while ($current) {
            array_unshift($path, $current['name']);
            if ($current['parent_id'] == 0) {
                break;
            }
            $current = \app\model\Category::find($current['parent_id']);
        }

        return implode(' > ', $path);
    }

    /**
     * 获取分类路径
     */
    public function getCategoryPathApi()
    {
        try {
            $categoryId = $this->request->get('category_id');
            if (empty($categoryId)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($categoryId);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $categoryPath = $this->getCategoryPath($category);

            return json([
                'code' => 1,
                'data' => [
                    'category_path' => $categoryPath,
                    'is_leaf' => $category->isLeafNode()
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取单个内容信息
     */
    public function getContent()
    {
        try {
            $id = $this->request->get('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::with(['category'])->find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            // 获取分类路径
            $categoryPath = '';
            if ($content['category']) {
                $categoryPath = $this->getCategoryPath($content['category']);
            }

            return json([
                'code' => 1,
                'data' => [
                    'id' => $content['id'],
                    'title' => $content['title'],
                    'category_id' => $content['category_id'],
                    'content' => $content['content'],
                    'sort_order' => $content['sort_order'],
                    'status' => $content['status'],
                    'category_path' => $categoryPath
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换内容状态
     */
    public function toggleContentStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->status = intval($status);
            $content->save();

            $statusText = $status == 1 ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "内容{$statusText}成功"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除内容
     */
    public function deleteContent()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->delete();
            return json(['code' => 1, 'msg' => '内容删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }



    /**
     * 批量删除内容（包括关联的卡密）
     */
    public function batchDeleteContents()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $ids = $this->request->post('ids', []);

            // 如果ids是JSON字符串，则解码
            if (is_string($ids)) {
                $ids = json_decode($ids, true);
            }

            if (empty($ids) || !is_array($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的内容']);
            }

            $deletedContentCount = 0;
            $deletedCardCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $content = \app\model\Content::find($id);
                    if ($content) {
                        // 先删除关联的卡密
                        $relatedCards = \app\model\Card::where('content_ids', 'like', '%"' . $id . '"%')->select();

                        foreach ($relatedCards as $card) {
                            $contentIds = json_decode($card->content_ids, true);
                            if (is_array($contentIds) && in_array($id, $contentIds)) {
                                $card->delete();
                                $deletedCardCount++;
                            }
                        }

                        // 再删除内容
                        $result = $content->delete();
                        if ($result) {
                            $deletedContentCount++;
                        } else {
                            $errors[] = "删除内容《{$content->title}》失败：数据库操作失败";
                        }
                    } else {
                        $errors[] = "内容ID {$id} 不存在";
                    }
                } catch (\Exception $e) {
                    $errors[] = "删除内容ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($deletedContentCount > 0) {
                $message = "成功删除 {$deletedContentCount} 个内容";
                if ($deletedCardCount > 0) {
                    $message .= "，同时删除了 {$deletedCardCount} 个关联卡密";
                }
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个失败";
                }

                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => [
                        'deleted_content_count' => $deletedContentCount,
                        'deleted_card_count' => $deletedCardCount,
                        'errors' => $errors
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有内容被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '强制删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存内容
     */
    public function saveContent()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必填字段
            if (empty($data['title'])) {
                return json(['code' => 0, 'msg' => '内容标题不能为空']);
            }

            if (empty($data['category_id'])) {
                return json(['code' => 0, 'msg' => '请选择分类']);
            }

            if (empty($data['content'])) {
                return json(['code' => 0, 'msg' => '内容详情不能为空']);
            }

            // 验证分类是否为叶子节点
            $category = \app\model\Category::find($data['category_id']);
            if (!$category) {
                return json(['code' => 0, 'msg' => '选择的分类不存在']);
            }

            if (!$category->isLeafNode()) {
                return json(['code' => 0, 'msg' => '只能在最低级分类中添加内容']);
            }

            // 准备保存数据
            $saveData = [
                'title' => $data['title'],
                'category_id' => $data['category_id'],
                'content' => $data['content'],
                'sort_order' => isset($data['sort_order']) ? intval($data['sort_order']) : 0,
                'status' => isset($data['status']) ? 1 : 0,
            ];

            if (isset($data['id']) && !empty($data['id'])) {
                // 编辑内容
                $content = \app\model\Content::find($data['id']);
                if (!$content) {
                    return json(['code' => 0, 'msg' => '内容不存在']);
                }
                $content->save($saveData);
                return json(['code' => 1, 'msg' => '内容更新成功']);
            } else {
                // 新增内容
                \app\model\Content::create($saveData);
                return json(['code' => 1, 'msg' => '内容添加成功']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新内容排序
     */
    public function updateContentSort()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $sortOrder = $this->request->post('sort_order', 0);

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->sort_order = intval($sortOrder);
            $content->save();

            return json(['code' => 1, 'msg' => '排序更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取内容统计数据
     */
    private function getContentStats()
    {
        try {
            // 总内容数
            $totalContents = \app\model\Content::count();

            // 启用的内容数
            $enabledContents = \app\model\Content::where('status', 1)->count();

            // 禁用的内容数
            $disabledContents = \app\model\Content::where('status', 0)->count();

            // 各分类的内容数
            $categoryStats = \app\model\Content::with(['category'])
                                              ->where('status', 1)
                                              ->group('category_id')
                                              ->field('category_id, count(*) as content_count')
                                              ->select();

            return [
                'total' => $totalContents,
                'enabled' => $enabledContents,
                'disabled' => $disabledContents,
                'category_stats' => $categoryStats
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'category_stats' => []
            ];
        }
    }

    /**
     * 系统设置
     */
    public function settings()
    {
        try {
            View::assign('current_page', 'settings');
            View::assign('page_title', '系统设置');

            return View::fetch('admin/settings');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 生成卡密
     */
    public function generateCards()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方法错误']);
        }

        try {
            $data = $this->request->post();

            // 新的验证逻辑：只需要分类和内容
            if (empty($data['category_id'])) {
                return json(['code' => 0, 'msg' => '请选择分类']);
            }

            if (empty($data['content_ids'])) {
                return json(['code' => 0, 'msg' => '请选择要关联的内容']);
            }

            // 获取卡密设置
            $settings = $this->getCardSettingsData();
            $quantity = $settings['card_generate_count'];

            // 生成批次ID
            $batchId = 'BATCH' . date('YmdHis');

            // 批量生成卡密
            $db = \think\facade\Db::connect();
            $cards = [];

            for ($i = 0; $i < $quantity; $i++) {
                $cardNumber = $this->generateCardNumberWithSettings($settings);

                // 检查卡密是否已存在
                while ($db->table('cards')->where('card_number', $cardNumber)->find()) {
                    $cardNumber = $this->generateCardNumberWithSettings($settings);
                }

                $expireTime = null;
                if ($settings['card_validity_days'] > 0) {
                    $expireTime = date('Y-m-d H:i:s', time() + $settings['card_validity_days'] * 24 * 3600);
                }

                $cards[] = [
                    'card_number' => $cardNumber,
                    'card_type' => '系统生成',
                    'batch_id' => $batchId,
                    'category_id' => $data['category_id'],
                    'content_ids' => json_encode($data['content_ids']),
                    'value' => 0,
                    'valid_days' => $settings['card_validity_days'],
                    'max_use_count' => $settings['card_usage_limit'],
                    'used_count' => 0,
                    'status' => 1,
                    'expire_time' => $expireTime,
                    'remark' => '根据系统设置自动生成',
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];
            }

            // 批量插入
            $db->table('cards')->insertAll($cards);

            // 获取关联的内容标题
            $contentTitles = [];
            if (!empty($data['content_ids'])) {
                $contents = \app\model\Content::whereIn('id', $data['content_ids'])->column('title');
                $contentTitles = array_values($contents);
            }

            // 获取生成的卡密号码
            $cardNumbers = array_column($cards, 'card_number');

            return json([
                'code' => 1,
                'msg' => "成功生成 {$quantity} 个卡密",
                'data' => [
                    'batch_id' => $batchId,
                    'cards' => $cardNumbers,
                    'content_titles' => $contentTitles,
                    'settings' => [
                        'card_success_title' => $settings['card_success_title'],
                        'card_success_content' => $settings['card_success_content']
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '生成卡密失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密设置数据
     */
    private function getCardSettingsData()
    {
        // 默认设置
        $defaults = [
            'card_generate_count' => 1,
            'card_length' => 8,
            'card_character_type' => 'mixed',
            'card_usage_limit' => 1,
            'card_prefix' => 'HZO-',
            'card_suffix' => '',
            'card_validity_days' => 0,
            'card_separator' => '',
            'card_case_sensitive' => true,
            'card_auto_delete' => true,
            'card_log_usage' => true,
            'card_description' => '此卡密仅限本站使用，请妥善保管。如有问题请联系客服。',
            'card_success_title' => '卡密生成成功',
            'card_success_content' => "内容标题：{CONTENT_TITLES}\n卡密：{CARD_NUMBERS}\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）"
        ];

        // 从配置文件获取设置
        $configFile = app()->getRootPath() . 'config/card_settings.php';
        $configSettings = [];
        if (file_exists($configFile)) {
            $configSettings = include $configFile;
        }

        // 合并默认设置和配置文件设置
        return array_merge($defaults, $configSettings);
    }

    /**
     * 获取前端设置数据
     */
    private function getFrontendSettingsData()
    {
        $db = \think\facade\Db::connect();

        // 默认前端设置
        $defaults = [
            'site_logo' => '',
            'site_title' => '红嘴鸥教育',
            'site_description' => '电子资料兑换系统',
            'promotion_enabled' => '1',
            'promotion_title' => '您还可以点击以下按钮获取更多免费资源',
            'promotion_btn1_text' => '电子资料包',
            'promotion_btn1_url' => '#',
            'promotion_btn2_text' => '免费网课',
            'promotion_btn2_url' => '#',
            'promotion_btn3_text' => '官方网站',
            'promotion_btn3_url' => '#',
            'promotion_contact_text' => '唯一售后微信：',
            'promotion_contact_value' => 'hzoedu888',
            'exchange_success_message' => '兑换成功！请查看下方内容',
            'exchange_error_message' => '兑换失败，请检查卡密是否正确',
            'page_footer_notice' => '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'
        ];

        try {
            // 从 settings 表获取前端设置
            $settings = $db->table('settings')
                          ->whereIn('key', array_keys($defaults))
                          ->column('value', 'key');

            // 合并默认值和数据库值
            return array_merge($defaults, $settings);

        } catch (\Exception $e) {
            return $defaults;
        }
    }

    /**
     * 根据设置生成卡密号码
     */
    private function generateCardNumberWithSettings($settings): string
    {
        $prefix = $settings['card_prefix'] ?? '';
        $suffix = $settings['card_suffix'] ?? '';
        $length = $settings['card_length'] ?? 8;
        $charType = $settings['card_character_type'] ?? 'mixed';
        $separator = $settings['card_separator'] ?? '';

        // 字符集
        $chars = [
            'mixed' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
            'numbers' => '0123456789',
            'letters' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'uppercase' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'lowercase' => 'abcdefghijklmnopqrstuvwxyz',
            'alphanumeric' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        ];

        $charSet = $chars[$charType] ?? $chars['mixed'];
        $cardBody = '';

        for ($i = 0; $i < $length; $i++) {
            if ($separator && $i > 0 && $i % 4 === 0) {
                $cardBody .= $separator;
            }
            $cardBody .= $charSet[rand(0, strlen($charSet) - 1)];
        }

        return $prefix . $cardBody . $suffix;
    }

    /**
     * 生成卡密号码（兼容旧方法）
     */
    private function generateCardNumber(): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $segments = [];

        for ($i = 0; $i < 4; $i++) {
            $segment = '';
            for ($j = 0; $j < 4; $j++) {
                $segment .= $chars[random_int(0, strlen($chars) - 1)];
            }
            $segments[] = $segment;
        }

        return implode('-', $segments);
    }

    /**
     * 卡密设置
     */
    public function cardSettings()
    {
        try {
            // 读取卡密设置
            $configFile = app()->getRootPath() . 'config/card_settings.php';
            $cardSettings = [];
            if (file_exists($configFile)) {
                $cardSettings = include $configFile;
            }

            // 设置默认值
            $defaultSettings = [
                'card_generate_count' => 1,
                'card_length' => 8,
                'card_character_type' => 'mixed',
                'card_usage_limit' => 1,
                'card_prefix' => 'HZO-',
                'card_suffix' => '',
                'card_validity_days' => 0,
                'card_separator' => '',
                'card_case_sensitive' => 1,
                'card_auto_delete' => 1,
                'card_log_usage' => 1,
                'card_description' => '此卡密仅限本站使用，请妥善保管。如有问题请联系客服。',
                'card_success_title' => '卡密生成成功',
                'card_success_content' => '内容跟标题\n卡密：变量\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）',
            ];

            $cardSettings = array_merge($defaultSettings, $cardSettings);

            View::assign('current_page', 'card-settings');
            View::assign('page_title', '卡密设置');
            View::assign('cardSettings', $cardSettings);
            return View::fetch('admin/card-settings');
        } catch (\Exception $e) {
            return $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 前端设置
     */
    public function frontendSettings()
    {
        try {
            // 读取前端设置
            $configFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($configFile)) {
                $frontendSettings = include $configFile;
            }

            // 设置默认值
            $defaultSettings = [
                'site_title' => '',
                'site_description' => '',
                'promotion_enabled' => 0,
                'promotion_title' => '',
                'promotion_btn1_text' => '',
                'promotion_btn1_url' => '',
                'promotion_btn2_text' => '',
                'promotion_btn2_url' => '',
                'promotion_btn3_text' => '',
                'promotion_btn3_url' => '',
                'promotion_contact_text' => '',
                'promotion_contact_value' => '',
                'exchange_success_message' => '',
                'exchange_error_message' => '',
                'page_footer_notice' => '',
            ];

            $frontendSettings = array_merge($defaultSettings, $frontendSettings);

            View::assign('current_page', 'frontend-settings');
            View::assign('page_title', '前端设置');
            View::assign('frontendSettings', $frontendSettings);
            return View::fetch('admin/frontend-settings');
        } catch (\Exception $e) {
            return $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 保存卡密设置
     */
    public function saveCardSettings()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必要字段
            $rules = [
                'card_generate_count' => 'require|number|between:1,1000',
                'card_length' => 'require|number|between:4,32',
                'card_character_type' => 'require|in:mixed,numbers,letters,uppercase,lowercase,alphanumeric',
                'card_usage_limit' => 'require|number|between:1,999',
                'card_validity_days' => 'require|number|egt:0',
            ];

            $validate = validate($rules);
            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }

            // 这里应该保存到配置文件或数据库
            // 暂时使用文件存储，实际项目中建议使用数据库
            $configFile = app()->getRootPath() . 'config/card_settings.php';
            $configData = [
                'card_generate_count' => (int)$data['card_generate_count'],
                'card_length' => (int)$data['card_length'],
                'card_character_type' => $data['card_character_type'],
                'card_usage_limit' => (int)$data['card_usage_limit'],
                'card_prefix' => $data['card_prefix'] ?? '',
                'card_suffix' => $data['card_suffix'] ?? '',
                'card_validity_days' => (int)$data['card_validity_days'],
                'card_separator' => $data['card_separator'] ?? '',
                'card_case_sensitive' => isset($data['card_case_sensitive']) ? 1 : 0,
                'card_auto_delete' => isset($data['card_auto_delete']) ? 1 : 0,
                'card_log_usage' => isset($data['card_log_usage']) ? 1 : 0,
                'card_description' => $data['card_description'] ?? '',
                'card_success_title' => $data['card_success_title'] ?? '卡密生成成功',
                'card_success_content' => $data['card_success_content'] ?? '',
            ];

            // 使用JSON编码然后转换为PHP数组格式，避免var_export的转义问题
            $configContent = "<?php\nreturn " . $this->arrayToPhpCode($configData) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return json(['code' => 0, 'msg' => '配置文件保存失败']);
            }

            return json(['code' => 1, 'msg' => '卡密设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 将数组转换为PHP代码，正确处理换行符
     */
    private function arrayToPhpCode($array, $indent = 0)
    {
        $indentStr = str_repeat('  ', $indent);
        $result = "array (\n";

        foreach ($array as $key => $value) {
            $result .= $indentStr . "  " . var_export($key, true) . " => ";

            if (is_array($value)) {
                $result .= $this->arrayToPhpCode($value, $indent + 1);
            } else {
                // 对字符串进行特殊处理，保持换行符
                if (is_string($value)) {
                    $result .= "'" . addslashes($value) . "'";
                } else {
                    $result .= var_export($value, true);
                }
            }
            $result .= ",\n";
        }

        $result .= $indentStr . ")";
        return $result;
    }

    /**
     * 保存前端设置
     */
    public function saveFrontendSettings()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 这里应该保存到配置文件或数据库
            $configFile = app()->getRootPath() . 'config/frontend_settings.php';
            $configData = [
                'site_title' => $data['site_title'] ?? '',
                'site_description' => $data['site_description'] ?? '',
                'promotion_enabled' => isset($data['promotion_enabled']) ? 1 : 0,
                'promotion_title' => $data['promotion_title'] ?? '',
                'promotion_btn1_text' => $data['promotion_btn1_text'] ?? '',
                'promotion_btn1_url' => $data['promotion_btn1_url'] ?? '',
                'promotion_btn2_text' => $data['promotion_btn2_text'] ?? '',
                'promotion_btn2_url' => $data['promotion_btn2_url'] ?? '',
                'promotion_btn3_text' => $data['promotion_btn3_text'] ?? '',
                'promotion_btn3_url' => $data['promotion_btn3_url'] ?? '',
                'promotion_contact_text' => $data['promotion_contact_text'] ?? '',
                'promotion_contact_value' => $data['promotion_contact_value'] ?? '',
                'exchange_success_message' => $data['exchange_success_message'] ?? '',
                'exchange_error_message' => $data['exchange_error_message'] ?? '',
                'page_footer_notice' => $data['page_footer_notice'] ?? '',
            ];

            $configContent = "<?php\nreturn " . var_export($configData, true) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return json(['code' => 0, 'msg' => '配置文件保存失败']);
            }

            return json(['code' => 1, 'msg' => '前端设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
}
