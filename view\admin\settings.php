<?php
$page_title = '系统设置';
$current_page = 'settings';

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">系统设置</h2>
        <p class="text-muted mb-0">配置系统的基本参数和功能选项</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveAllSettings()">
            <i class="fas fa-save me-2"></i>保存所有设置
        </button>
    </div>
</div>

<!-- 设置选项卡 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                <i class="fas fa-cog me-2"></i>基本设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="card-tab" data-bs-toggle="tab" data-bs-target="#card" type="button" role="tab">
                <i class="fas fa-credit-card me-2"></i>卡密设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="frontend-tab" data-bs-toggle="tab" data-bs-target="#frontend" type="button" role="tab">
                <i class="fas fa-desktop me-2"></i>前端设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                <i class="fas fa-address-book me-2"></i>联系方式
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                <i class="fas fa-shield-alt me-2"></i>安全设置
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="settingsTabContent">
        <!-- 基本设置 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
            <div class="p-4">
                <form id="basicForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">网站名称</label>
                            <input type="text" class="form-control" name="site_name" value="红嘴鸥教育">
                            <div class="form-text">显示在网站标题和页面头部的名称</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站描述</label>
                            <input type="text" class="form-control" name="site_description" value="电子资料兑换系统">
                            <div class="form-text">网站的简短描述信息</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站关键词</label>
                            <input type="text" class="form-control" name="site_keywords" value="卡密,兑换,资料,教程">
                            <div class="form-text">用逗号分隔多个关键词</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站Logo</label>
                            <input type="file" class="form-control" name="site_logo" accept="image/*">
                            <div class="form-text">建议尺寸：200x60像素</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站状态</label>
                            <select class="form-select" name="site_status">
                                <option value="1" selected>正常运行</option>
                                <option value="0">维护中</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">时区设置</label>
                            <select class="form-select" name="timezone">
                                <option value="Asia/Shanghai" selected>Asia/Shanghai (北京时间)</option>
                                <option value="UTC">UTC (协调世界时)</option>
                                <option value="America/New_York">America/New_York (纽约时间)</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">维护公告</label>
                            <textarea class="form-control" name="maintenance_notice" rows="3" placeholder="网站维护时显示的公告内容"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">网站公告</label>
                            <textarea class="form-control" name="site_notice" rows="4" placeholder="显示在网站首页的公告内容">欢迎使用红嘴鸥教育资料兑换系统！请使用有效的卡密进行兑换。</textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>





        <!-- 卡密设置 -->
        <div class="tab-pane fade" id="card" role="tabpanel">
            <div class="p-4">
                <form id="cardForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">每次生成数量</label>
                            <input type="number" class="form-control" name="card_generate_count" value="1" min="1" max="1000">
                            <div class="form-text">单次批量生成卡密的数量</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">卡密长度</label>
                            <input type="number" class="form-control" name="card_length" value="8" min="4" max="32">
                            <div class="form-text">卡密字符串的长度（不包含前缀）</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">字符组合</label>
                            <select class="form-select" name="card_character_type">
                                <option value="mixed" selected>数字+字母</option>
                                <option value="numbers">仅数字</option>
                                <option value="letters">仅字母</option>
                                <option value="uppercase">仅大写字母</option>
                                <option value="lowercase">仅小写字母</option>
                                <option value="alphanumeric">数字+大写字母</option>
                            </select>
                            <div class="form-text">卡密包含的字符类型</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">使用次数</label>
                            <input type="number" class="form-control" name="card_usage_limit" value="1" min="1" max="999">
                            <div class="form-text">每个卡密可以使用的次数</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">卡密前缀</label>
                            <input type="text" class="form-control" name="card_prefix" value="HZO-" maxlength="10">
                            <div class="form-text">卡密的固定前缀，留空则无前缀</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">卡密后缀</label>
                            <input type="text" class="form-control" name="card_suffix" value="" maxlength="10">
                            <div class="form-text">卡密的固定后缀，留空则无后缀</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">有效期设置</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="card_validity_days" value="0" min="0" max="3650">
                                <span class="input-group-text">天</span>
                            </div>
                            <div class="form-text">卡密的有效期，0表示永不过期</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">分隔符</label>
                            <select class="form-select" name="card_separator">
                                <option value="">无分隔符</option>
                                <option value="-">短横线（-）</option>
                                <option value="_">下划线（_）</option>
                                <option value=".">点号（.）</option>
                            </select>
                            <div class="form-text">卡密中间的分隔符，每4位插入一个</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">
                                <i class="fas fa-eye me-2"></i>卡密格式预览
                            </label>
                            <div class="text-center py-3">
                                <code id="cardPreview">HZO-A1B2C3D4</code>
                            </div>
                            <div class="form-text text-center">根据当前设置生成的卡密格式示例</div>
                        </div>

                        <!-- 功能开关设置 -->
                        <div class="col-12">
                            <h6 class="settings-section-title">
                                <i class="fas fa-toggle-on me-2"></i>功能开关
                            </h6>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-settings-switch h-100">
                                <div class="card-body text-center">
                                    <div class="form-check form-switch d-flex justify-content-center mb-2">
                                        <input class="form-check-input" type="checkbox" name="card_case_sensitive" checked>
                                    </div>
                                    <h6 class="card-title mb-2">区分大小写</h6>
                                    <p class="card-text text-muted">验证卡密时是否区分字母大小写</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-settings-switch h-100">
                                <div class="card-body text-center">
                                    <div class="form-check form-switch d-flex justify-content-center mb-2">
                                        <input class="form-check-input" type="checkbox" name="card_auto_delete" checked>
                                    </div>
                                    <h6 class="card-title mb-2">使用后自动删除</h6>
                                    <p class="card-text text-muted">卡密使用完毕后是否自动从数据库删除</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card card-settings-switch h-100">
                                <div class="card-body text-center">
                                    <div class="form-check form-switch d-flex justify-content-center mb-2">
                                        <input class="form-check-input" type="checkbox" name="card_log_usage" checked>
                                    </div>
                                    <h6 class="card-title mb-2">记录使用日志</h6>
                                    <p class="card-text text-muted">是否记录卡密的使用情况和用户信息</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">卡密说明</label>
                            <textarea class="form-control" name="card_description" rows="3" placeholder="请输入卡密使用说明...">此卡密仅限本站使用，请妥善保管。如有问题请联系客服。</textarea>
                            <div class="form-text">显示给用户的卡密使用说明</div>
                        </div>

                        <!-- 生成成功弹窗设置 -->
                        <div class="col-12 mt-4">
                            <h6 class="settings-section-title">
                                <i class="fas fa-window-restore me-2"></i>生成成功弹窗
                            </h6>
                        </div>
                        <div class="col-12">
                            <label class="form-label">弹窗标题</label>
                            <input type="text" class="form-control" name="card_success_title" value="卡密生成成功" maxlength="50">
                            <div class="form-text">生成成功弹窗的标题</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">弹窗内容</label>
                            <textarea class="form-control" name="card_success_content" rows="10" placeholder="请输入弹窗内容模板">内容跟标题
卡密：变量
兑换地址：https://kmdh.hzoedu.com

使用方法：
1. 复制卡密，点击兑换地址进入
2. 输入卡密，点击兑换按钮
3. 即可获取下载链接
4. 若需再次查看下载链接，点击【兑换记录查询】

（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）</textarea>
                            <div class="form-text">
                                弹窗显示的完整内容模板，支持动态变量替换
                            </div>

                            <!-- 变量使用说明 -->
                            <div class="alert alert-info mt-2">
                                <h6 class="alert-heading mb-2">
                                    <i class="fas fa-info-circle me-2"></i>可用变量说明
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2"><strong>动态变量：</strong></p>
                                        <ul class="mb-0 small">
                                            <li><code>{CONTENT_TITLES}</code> - 关联的内容标题（多个用顿号分隔）</li>
                                            <li><code>{CARD_NUMBERS}</code> - 生成的卡密号码（多个换行显示）</li>
                                            <li><code>{CARD_COUNT}</code> - 生成的卡密数量</li>
                                            <li><code>{BATCH_ID}</code> - 卡密批次ID</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2"><strong>使用示例：</strong></p>
                                        <div class="small">
                                            <code>内容标题：{CONTENT_TITLES}</code><br>
                                            <code>卡密：{CARD_NUMBERS}</code><br>
                                            <code>共生成 {CARD_COUNT} 个卡密</code><br>
                                            <code>批次号：{BATCH_ID}</code>
                                        </div>
                                    </div>
                                </div>
                                <hr class="my-2">
                                <p class="mb-0 small text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>提示：</strong>变量会在生成卡密成功后自动替换为实际内容，请确保变量名称正确（区分大小写）
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 前端设置 -->
        <div class="tab-pane fade" id="frontend" role="tabpanel">
            <div class="p-4">
                <form id="frontendForm">
                    <div class="row g-4">
                        <!-- 网站基本信息 -->
                        <div class="col-12">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-globe me-2"></i>网站基本信息
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站Logo</label>
                            <input type="file" class="form-control" name="site_logo" accept="image/*">
                            <div class="form-text">建议尺寸：200x60像素，支持PNG、JPG格式</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站标题</label>
                            <input type="text" class="form-control" name="site_title" placeholder="请输入网站标题">
                            <div class="form-text">显示在浏览器标题栏和页面头部</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">网站描述</label>
                            <textarea class="form-control" name="site_description" rows="3" placeholder="请输入网站描述"></textarea>
                            <div class="form-text">显示在页面头部的描述信息</div>
                        </div>

                        <!-- 推广模块设置 -->
                        <div class="col-12 mt-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-bullhorn me-2"></i>推广模块设置
                            </h5>
                        </div>
                        <div class="col-12">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="promotion_enabled" id="promotionEnabled" value="1">
                                <label class="form-check-label" for="promotionEnabled">启用推广模块</label>
                            </div>
                            <div class="form-text">兑换成功后是否显示推广模块</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">推广标题</label>
                            <input type="text" class="form-control" name="promotion_title" placeholder="您还可以点击以下按钮获取更多免费资源">
                            <div class="form-text">推广模块的标题文字</div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">按钮1文字</label>
                            <input type="text" class="form-control" name="promotion_btn1_text" placeholder="电子资料包">
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">按钮1链接</label>
                            <input type="url" class="form-control" name="promotion_btn1_url" placeholder="https://example.com">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">按钮2文字</label>
                            <input type="text" class="form-control" name="promotion_btn2_text" placeholder="免费网课">
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">按钮2链接</label>
                            <input type="url" class="form-control" name="promotion_btn2_url" placeholder="https://example.com">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">按钮3文字</label>
                            <input type="text" class="form-control" name="promotion_btn3_text" placeholder="官方网站">
                        </div>
                        <div class="col-md-8">
                            <label class="form-label">按钮3链接</label>
                            <input type="url" class="form-control" name="promotion_btn3_url" placeholder="https://example.com">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">联系方式文字</label>
                            <input type="text" class="form-control" name="promotion_contact_text" placeholder="唯一售后微信：">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">联系方式值</label>
                            <input type="text" class="form-control" name="promotion_contact_value" placeholder="hzoedu888">
                        </div>

                        <!-- 弹窗提示设置 -->
                        <div class="col-12 mt-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-comment-dots me-2"></i>弹窗提示设置
                            </h5>
                        </div>
                        <div class="col-12">
                            <label class="form-label">兑换成功提示</label>
                            <textarea class="form-control" name="exchange_success_message" rows="3" placeholder="兑换成功！请查看下方内容"></textarea>
                            <div class="form-text">兑换成功时显示的提示信息</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">兑换失败提示</label>
                            <textarea class="form-control" name="exchange_error_message" rows="3" placeholder="兑换失败，请检查卡密是否正确"></textarea>
                            <div class="form-text">兑换失败时显示的提示信息</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">页面底部说明</label>
                            <textarea class="form-control" name="page_footer_notice" rows="4" placeholder="此为电子资料兑换下载系统，兑换后不支持任何理由的退换货"></textarea>
                            <div class="form-text">显示在页面底部的说明文字</div>
                        </div>

                    </div>
                </form>
            </div>
        </div>

        <!-- 联系方式 -->
        <div class="tab-pane fade" id="contact" role="tabpanel">
            <div class="p-4">
                <form id="contactForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">客服微信</label>
                            <input type="text" class="form-control" name="contact_wechat" value="hzoedu888">
                            <div class="form-text">客服微信号</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服QQ</label>
                            <input type="text" class="form-control" name="contact_qq" value="">
                            <div class="form-text">客服QQ号码</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服电话</label>
                            <input type="text" class="form-control" name="contact_phone" value="">
                            <div class="form-text">客服联系电话</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服邮箱</label>
                            <input type="email" class="form-control" name="contact_email" value="">
                            <div class="form-text">客服邮箱地址</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">微信二维码</label>
                            <input type="file" class="form-control" name="wechat_qrcode" accept="image/*">
                            <div class="form-text">上传微信客服二维码图片</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">联系说明</label>
                            <textarea class="form-control" name="contact_notice" rows="4">如有问题请联系客服，工作时间：周一至周五 9:00-18:00</textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="tab-pane fade" id="security" role="tabpanel">
            <div class="p-4">
                <form id="securityForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">登录失败锁定次数</label>
                            <input type="number" class="form-control" name="login_max_attempts" value="5" min="3" max="10">
                            <div class="form-text">连续登录失败多少次后锁定账户</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">账户锁定时间（分钟）</label>
                            <input type="number" class="form-control" name="login_lock_time" value="30" min="5" max="1440">
                            <div class="form-text">账户被锁定的时间长度</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">会话超时时间（分钟）</label>
                            <input type="number" class="form-control" name="session_timeout" value="120" min="30" max="1440">
                            <div class="form-text">用户无操作后自动退出的时间</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">密码最小长度</label>
                            <input type="number" class="form-control" name="password_min_length" value="6" min="6" max="20">
                            <div class="form-text">管理员密码的最小长度要求</div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_ip_whitelist" value="1">
                                <label class="form-check-label">启用IP白名单</label>
                            </div>
                            <div class="form-text">只允许白名单内的IP访问管理后台</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">IP白名单</label>
                            <textarea class="form-control" name="ip_whitelist" rows="4" placeholder="每行一个IP地址，支持CIDR格式，如：***********/24"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_operation_log" value="1" checked>
                                <label class="form-check-label">启用操作日志</label>
                            </div>
                            <div class="form-text">记录管理员的所有操作行为</div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 保存所有设置
function saveAllSettings() {
    const forms = ["basicForm", "cardForm", "frontendForm", "contactForm", "securityForm"];
    const allData = {};

    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            const formData = new FormData(form);

            for (let [key, value] of formData.entries()) {
                if (allData[key]) {
                    // 处理多选框
                    if (Array.isArray(allData[key])) {
                        allData[key].push(value);
                    } else {
                        allData[key] = [allData[key], value];
                    }
                } else {
                    allData[key] = value;
                }
            }
        }
    });

    // 发送AJAX请求到后端
    fetch(\'/admin/saveSettings\', {
        method: \'POST\',
        headers: {
            \'Content-Type\': \'application/x-www-form-urlencoded\'
        },
        body: new URLSearchParams(allData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            AdminUtils.showMessage("设置保存成功！", "success");
            // 保存成功后重新加载设置
            setTimeout(() => {
                loadSettings();
            }, 500);
        } else {
            AdminUtils.showMessage(data.msg || "保存失败", "error");
        }
    })
    .catch(error => {
        console.error(\'保存设置错误:\', error);
        AdminUtils.showMessage("保存失败：网络错误", "error");
    });
}

// 更新卡密预览
function updateCardPreview() {
    const prefixInput = document.querySelector("input[name=card_prefix]");
    const suffixInput = document.querySelector("input[name=card_suffix]");
    const lengthInput = document.querySelector("input[name=card_length]");
    const charTypeSelect = document.querySelector("select[name=card_character_type]");
    const separatorSelect = document.querySelector("select[name=card_separator]");

    const prefix = prefixInput ? prefixInput.value : "";
    const suffix = suffixInput ? suffixInput.value : "";
    const length = lengthInput ? parseInt(lengthInput.value) : 8;
    const charType = charTypeSelect ? charTypeSelect.value : "mixed";
    const separator = separatorSelect ? separatorSelect.value : "";

    // 生成示例卡密
    let sampleCard = "";
    const chars = {
        "mixed": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
        "numbers": "0123456789",
        "letters": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "uppercase": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "lowercase": "abcdefghijklmnopqrstuvwxyz",
        "alphanumeric": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    };

    const charSet = chars[charType] || chars["mixed"];

    for (let i = 0; i < length; i++) {
        if (separator && i > 0 && i % 4 === 0) {
            sampleCard += separator;
        }
        sampleCard += charSet[Math.floor(Math.random() * charSet.length)];
    }

    const fullCard = prefix + sampleCard + suffix;
    const previewElement = document.getElementById("cardPreview");
    if (previewElement) {
        previewElement.textContent = fullCard;
    }
}

// 单独保存某个表单
function saveForm(formId) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    console.log(\'保存\' + formId + \':\', data);
    AdminUtils.showMessage("设置保存成功！", "success");
}

// 加载设置数据
function loadSettings() {
    // 加载卡密设置
    fetch(\'/admin/getCardSettings\')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const settings = data.data;

            // 填充卡密设置表单
            const cardForm = document.getElementById(\'cardForm\');
            if (cardForm) {
                Object.keys(settings).forEach(key => {
                    const element = cardForm.querySelector(\'[name="\' + key + \'"]\');
                    if (element) {
                        if (element.type === \'checkbox\') {
                            element.checked = settings[key];
                        } else {
                            element.value = settings[key];
                        }
                    }
                });
            }

            // 更新卡密预览
            updateCardPreview();
        }
    })
    .catch(error => {
        console.error(\'加载卡密设置错误:\', error);
    });

    // 加载前端设置
    fetch(\'/admin/getFrontendSettings\')
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const settings = data.data;

            // 填充前端设置表单
            const frontendForm = document.getElementById(\'frontendForm\');
            if (frontendForm) {
                Object.keys(settings).forEach(key => {
                    const element = frontendForm.querySelector(\'[name="\' + key + \'"]\');
                    if (element) {
                        if (element.type === \'checkbox\') {
                            element.checked = settings[key] === true || settings[key] === \'1\';
                        } else if (element.type === \'file\') {
                            // 文件输入框不能设置value，跳过
                        } else {
                            element.value = settings[key];
                        }
                    }
                });
            }
        }
    })
    .catch(error => {
        console.error(\'加载前端设置错误:\', error);
    });
}

// 为每个表单添加保存按钮事件
document.addEventListener("DOMContentLoaded", function() {
    const forms = ["basicForm", "cardForm", "frontendForm", "contactForm", "securityForm"];

    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener("submit", function(e) {
                e.preventDefault();
                saveForm(formId);
            });
        }
    });

    // 加载设置数据
    loadSettings();

    // 监听卡密设置变化，实时更新预览
    const cardForm = document.getElementById("cardForm");
    if (cardForm) {
        cardForm.addEventListener("input", updateCardPreview);
        cardForm.addEventListener("change", updateCardPreview);
    }
});
</script>
';

include 'layout.php';
?>
