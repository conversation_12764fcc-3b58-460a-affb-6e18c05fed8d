<?php
$page_title = '系统设置';
$current_page = 'settings';

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">系统设置</h2>
        <p class="text-muted mb-0">配置系统的基本参数和功能选项</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveAllSettings()">
            <i class="fas fa-save me-2"></i>保存所有设置
        </button>
    </div>
</div>

<!-- 设置选项卡 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                <i class="fas fa-globe me-2"></i>网站设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                <i class="fas fa-address-book me-2"></i>联系方式
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                <i class="fas fa-shield-alt me-2"></i>安全设置
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="settingsTabContent">
        <!-- 网站设置 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
            <div class="p-4">
                <form id="basicForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">网站名称</label>
                            <input type="text" class="form-control" name="site_name" value="红嘴鸥教育">
                            <div class="form-text">显示在网站标题和页面头部的名称</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站描述</label>
                            <input type="text" class="form-control" name="site_description" value="电子资料兑换系统">
                            <div class="form-text">网站的简短描述信息</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站关键词</label>
                            <input type="text" class="form-control" name="site_keywords" value="卡密,兑换,资料,教程">
                            <div class="form-text">用逗号分隔多个关键词</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站Logo</label>
                            <input type="file" class="form-control" name="site_logo" accept="image/*">
                            <div class="form-text">建议尺寸：200x60像素</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">网站状态</label>
                            <select class="form-select" name="site_status">
                                <option value="1" selected>正常运行</option>
                                <option value="0">维护中</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">时区设置</label>
                            <select class="form-select" name="timezone">
                                <option value="Asia/Shanghai" selected>Asia/Shanghai (北京时间)</option>
                                <option value="UTC">UTC (协调世界时)</option>
                                <option value="America/New_York">America/New_York (纽约时间)</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">维护公告</label>
                            <textarea class="form-control" name="maintenance_notice" rows="3" placeholder="网站维护时显示的公告内容"></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">网站公告</label>
                            <textarea class="form-control" name="site_notice" rows="4" placeholder="显示在网站首页的公告内容">欢迎使用红嘴鸥教育资料兑换系统！请使用有效的卡密进行兑换。</textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 联系方式 -->
        <div class="tab-pane fade" id="contact" role="tabpanel">
            <div class="p-4">
                <form id="contactForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">客服微信</label>
                            <input type="text" class="form-control" name="contact_wechat" value="hzoedu888">
                            <div class="form-text">客服微信号</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服QQ</label>
                            <input type="text" class="form-control" name="contact_qq" value="">
                            <div class="form-text">客服QQ号码</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服邮箱</label>
                            <input type="email" class="form-control" name="contact_email" value="">
                            <div class="form-text">客服邮箱地址</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">客服电话</label>
                            <input type="text" class="form-control" name="contact_phone" value="">
                            <div class="form-text">客服联系电话</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">工作时间</label>
                            <input type="text" class="form-control" name="work_time" value="周一至周五 9:00-18:00">
                            <div class="form-text">客服工作时间说明</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">联系地址</label>
                            <textarea class="form-control" name="contact_address" rows="3" placeholder="公司或机构地址"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="tab-pane fade" id="security" role="tabpanel">
            <div class="p-4">
                <form id="securityForm">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">登录失败限制</label>
                            <input type="number" class="form-control" name="login_fail_limit" value="5" min="1" max="20">
                            <div class="form-text">连续登录失败次数限制</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">锁定时间</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="lockout_duration" value="30" min="1">
                                <span class="input-group-text">分钟</span>
                            </div>
                            <div class="form-text">账户锁定时间</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">会话超时</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="session_timeout" value="120" min="10">
                                <span class="input-group-text">分钟</span>
                            </div>
                            <div class="form-text">用户会话超时时间</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">密码最小长度</label>
                            <input type="number" class="form-control" name="password_min_length" value="6" min="4" max="20">
                            <div class="form-text">用户密码最小长度要求</div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_ip_whitelist" value="1">
                                <label class="form-check-label">启用IP白名单</label>
                            </div>
                            <div class="form-text">只允许白名单内的IP访问管理后台</div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">IP白名单</label>
                            <textarea class="form-control" name="ip_whitelist" rows="4" placeholder="每行一个IP地址，支持CIDR格式，如：***********/24"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_operation_log" value="1" checked>
                                <label class="form-check-label">启用操作日志</label>
                            </div>
                            <div class="form-text">记录管理员的所有操作行为</div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 保存所有设置
function saveAllSettings() {
    const forms = ["basicForm", "contactForm", "securityForm"];
    const allData = {};
    
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                allData[key] = value;
            }
        }
    });
    
    // 处理复选框
    const checkboxes = document.querySelectorAll("input[type=checkbox]");
    checkboxes.forEach(checkbox => {
        allData[checkbox.name] = checkbox.checked ? "1" : "0";
    });
    
    console.log("保存设置:", allData);
    AdminUtils.showMessage("设置保存成功", "success");
}
</script>
';

include 'layout.php';
