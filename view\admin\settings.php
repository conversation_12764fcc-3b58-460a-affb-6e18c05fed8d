<?php
$page_title = '系统设置';
$current_page = 'settings';

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">系统设置</h2>
        <p class="text-muted mb-0">配置系统的基本参数和功能选项</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveAllSettings()">
            <i class="fas fa-save me-2"></i>保存所有设置
        </button>
    </div>
</div>

<!-- 设置选项卡 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                <i class="fas fa-globe me-2"></i>网站设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                <i class="fas fa-address-book me-2"></i>联系方式
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                <i class="fas fa-shield-alt me-2"></i>安全设置
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="settingsTabContent">
        <!-- 网站设置 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
            <div class="p-4">
                <form id="basicForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">网站名称</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="site_name" value="红嘴鸥教育">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">显示在网站标题和页面头部的名称</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">网站描述</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="site_description" value="电子资料兑换系统">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">网站的简短描述信息</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">网站关键词</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="site_keywords" value="卡密,兑换,资料,教程">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">用逗号分隔多个关键词</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">网站Logo</label>
                                </div>
                                <div class="col-5">
                                    <input type="file" class="form-control" name="site_logo" accept="image/*">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">建议尺寸：200x60像素</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">网站状态</label>
                                </div>
                                <div class="col-5">
                                    <select class="form-select" name="site_status">
                                        <option value="1" selected>正常运行</option>
                                        <option value="0">维护中</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">网站当前运行状态</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">时区设置</label>
                                </div>
                                <div class="col-5">
                                    <select class="form-select" name="timezone">
                                        <option value="Asia/Shanghai" selected>Asia/Shanghai (北京时间)</option>
                                        <option value="UTC">UTC (协调世界时)</option>
                                        <option value="America/New_York">America/New_York (纽约时间)</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">系统使用的时区设置</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">维护公告</label>
                                </div>
                                <div class="col-5">
                                    <textarea class="form-control" name="maintenance_notice" rows="3" placeholder="网站维护时显示的公告内容"></textarea>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-start pt-2">网站维护时显示给用户的公告</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">网站公告</label>
                                </div>
                                <div class="col-5">
                                    <textarea class="form-control" name="site_notice" rows="4" placeholder="显示在网站首页的公告内容">欢迎使用红嘴鸥教育资料兑换系统！请使用有效的卡密进行兑换。</textarea>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-start pt-2">显示在网站首页的公告内容</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 联系方式 -->
        <div class="tab-pane fade" id="contact" role="tabpanel">
            <div class="p-4">
                <form id="contactForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">客服微信</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="contact_wechat" value="hzoedu888">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">客服微信号</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">客服QQ</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="contact_qq" value="">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">客服QQ号码</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">客服邮箱</label>
                                </div>
                                <div class="col-5">
                                    <input type="email" class="form-control" name="contact_email" value="">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">客服邮箱地址</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">客服电话</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="contact_phone" value="">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">客服联系电话</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">工作时间</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="work_time" value="周一至周五 9:00-18:00">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">客服工作时间说明</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">联系地址</label>
                                </div>
                                <div class="col-5">
                                    <textarea class="form-control" name="contact_address" rows="3" placeholder="公司或机构地址"></textarea>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-start pt-2">公司或机构的详细地址</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="tab-pane fade" id="security" role="tabpanel">
            <div class="p-4">
                <form id="securityForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">登录失败限制</label>
                                </div>
                                <div class="col-5">
                                    <input type="number" class="form-control" name="login_fail_limit" value="5" min="1" max="20">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">连续登录失败次数限制</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">锁定时间</label>
                                </div>
                                <div class="col-5">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="lockout_duration" value="30" min="1">
                                        <span class="input-group-text">分钟</span>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">账户锁定时间</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">会话超时</label>
                                </div>
                                <div class="col-5">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="session_timeout" value="120" min="10">
                                        <span class="input-group-text">分钟</span>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">用户会话超时时间</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">密码最小长度</label>
                                </div>
                                <div class="col-5">
                                    <input type="number" class="form-control" name="password_min_length" value="6" min="4" max="20">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">用户密码最小长度要求</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">启用IP白名单</label>
                                </div>
                                <div class="col-5">
                                    <div class="form-check form-switch d-flex align-items-center" style="min-height: 38px;">
                                        <input class="form-check-input" type="checkbox" name="enable_ip_whitelist" value="1">
                                        <label class="form-check-label ms-2">启用IP白名单</label>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">只允许白名单内的IP访问管理后台</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">IP白名单</label>
                                </div>
                                <div class="col-5">
                                    <textarea class="form-control" name="ip_whitelist" rows="4" placeholder="每行一个IP地址，支持CIDR格式，如：***********/24"></textarea>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-start pt-2">每行一个IP地址，支持CIDR格式</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">启用操作日志</label>
                                </div>
                                <div class="col-5">
                                    <div class="form-check form-switch d-flex align-items-center" style="min-height: 38px;">
                                        <input class="form-check-input" type="checkbox" name="enable_operation_log" value="1" checked>
                                        <label class="form-check-label ms-2">启用操作日志</label>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">记录管理员的所有操作行为</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 保存所有设置
function saveAllSettings() {
    const forms = ["basicForm", "contactForm", "securityForm"];
    const allData = {};
    
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                allData[key] = value;
            }
        }
    });
    
    // 处理复选框
    const checkboxes = document.querySelectorAll("input[type=checkbox]");
    checkboxes.forEach(checkbox => {
        allData[checkbox.name] = checkbox.checked ? "1" : "0";
    });
    
    console.log("保存设置:", allData);
    AdminUtils.showMessage("设置保存成功", "success");
}
</script>
';

include 'layout.php';
