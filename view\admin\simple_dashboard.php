<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        .header a {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            background: #34495e;
            border-radius: 5px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }
        .recent {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .recent h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }
        .record {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .record:last-child {
            border-bottom: none;
        }
        .status-success {
            color: #27ae60;
            font-weight: bold;
        }
        .status-failed {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>卡密兑换管理系统</h1>
        <div>
            <span>欢迎，<?php echo isset($admin['nickname']) ? $admin['nickname'] : (isset($admin['username']) ? $admin['username'] : '管理员'); ?></span>
            <a href="/admin/logout">退出登录</a>
        </div>
    </div>
    
    <div class="container">
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>总卡密数</h3>
                <div class="number" id="totalCards">-</div>
            </div>
            <div class="stat-card">
                <h3>已使用</h3>
                <div class="number" id="usedCards">-</div>
            </div>
            <div class="stat-card">
                <h3>未使用</h3>
                <div class="number" id="unusedCards">-</div>
            </div>
            <div class="stat-card">
                <h3>今日兑换</h3>
                <div class="number" id="todayExchange">-</div>
            </div>
        </div>
        
        <div class="recent">
            <h3>最近兑换记录</h3>
            <div id="recentRecords">
                <p>正在加载...</p>
            </div>
        </div>
    </div>

    <script>
        // 加载统计数据
        fetch('/admin/getStats')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    const stats = data.data.overview;
                    document.getElementById('totalCards').textContent = stats.total_cards;
                    document.getElementById('usedCards').textContent = stats.used_cards;
                    document.getElementById('unusedCards').textContent = stats.unused_cards;
                    document.getElementById('todayExchange').textContent = stats.today_exchange;
                    
                    // 显示最近记录
                    const records = data.data.recent_exchanges;
                    let html = '';
                    if (records.length > 0) {
                        records.forEach(record => {
                            const statusClass = record.status == 1 ? 'status-success' : 'status-failed';
                            const statusText = record.status == 1 ? '成功' : '失败';
                            html += `
                                <div class="record">
                                    <div>
                                        <strong>${record.card_number}</strong> - ${record.card_type || '未知类型'}
                                    </div>
                                    <div>
                                        <span class="${statusClass}">${statusText}</span>
                                        <small>${record.exchange_time}</small>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html = '<p>暂无兑换记录</p>';
                    }
                    document.getElementById('recentRecords').innerHTML = html;
                }
            })
            .catch(error => {
                console.error('加载统计数据失败:', error);
                document.getElementById('recentRecords').innerHTML = '<p>加载失败</p>';
            });
    </script>
</body>
</html>
