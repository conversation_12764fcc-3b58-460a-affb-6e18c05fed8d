<?php include 'layout.php'; ?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-circle me-2"></i>个人资料
            </h1>
            <p class="text-muted mb-0">管理您的个人信息和账户设置</p>
        </div>
    </div>

    <div class="row">
        <!-- 个人信息卡片 -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-gradient-primary text-white border-0">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-id-card me-2"></i>基本信息
                    </h6>
                </div>
                <div class="card-body text-center p-3">
                    <!-- 头像区域 -->
                    <div class="mb-3">
                        <div class="avatar-container position-relative d-inline-block">
                            <img src="<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiM2Njc3ZWEiLz4KPHN2ZyB4PSIyNSIgeT0iMTUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI3MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgzVjlDMyA2Ljc5IDQuNzkgNSA3IDVIMTdDMTkuMjEgNSAyMSA2Ljc5IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K'; ?>"
                                 alt="头像" class="rounded-circle border border-2 border-white shadow" width="100" height="100" id="avatarPreview"
                                 style="object-fit: cover;">
                            <div class="avatar-overlay position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center"
                                 style="background: rgba(0,0,0,0.6); opacity: 0; transition: all 0.3s ease; cursor: pointer;">
                                <i class="fas fa-camera text-white fs-5"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 用户信息 -->
                    <div class="mb-3">
                        <h6 class="mb-1 fw-bold text-dark"><?php echo htmlspecialchars($admin['nickname'] ?: $admin['username']); ?></h6>
                        <p class="text-muted mb-2 small">@<?php echo htmlspecialchars($admin['username']); ?></p>
                        <span class="badge bg-success px-2 py-1 rounded-pill small">
                            <i class="fas fa-crown me-1"></i>管理员
                        </span>
                    </div>

                    <!-- 统计信息 -->
                    <div class="row g-0 border rounded-3 overflow-hidden mb-3">
                        <div class="col-6">
                            <div class="p-2 border-end bg-light">
                                <div class="d-flex flex-column">
                                    <span class="fw-bold text-primary small"><?php echo date('Y-m-d', strtotime($admin['create_time'])); ?></span>
                                    <small class="text-muted" style="font-size: 0.7rem;">注册时间</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-2 bg-light">
                                <div class="d-flex flex-column">
                                    <span class="fw-bold text-success small"><?php echo $admin['last_login_time'] ? date('m-d H:i', strtotime($admin['last_login_time'])) : '从未登录'; ?></span>
                                    <small class="text-muted" style="font-size: 0.7rem;">最后登录</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <?php if (!empty($admin['email']) || !empty($admin['phone'])): ?>
                    <div class="text-start">
                        <h6 class="text-muted mb-2 fw-bold small">联系方式</h6>
                        <?php if (!empty($admin['email'])): ?>
                        <div class="d-flex align-items-center mb-1">
                            <i class="fas fa-envelope text-primary me-2 small"></i>
                            <small class="text-muted" style="font-size: 0.75rem;"><?php echo htmlspecialchars($admin['email']); ?></small>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($admin['phone'])): ?>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-phone text-success me-2 small"></i>
                            <small class="text-muted" style="font-size: 0.75rem;"><?php echo htmlspecialchars($admin['phone']); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 编辑表单 -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-gradient-info text-white border-0">
                    <h6 class="m-0 fw-bold">
                        <i class="fas fa-edit me-2"></i>编辑资料
                    </h6>
                </div>
                <div class="card-body p-4">
                    <form id="profileForm" enctype="multipart/form-data">
                        <!-- 基本信息 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3 fw-bold">
                                <i class="fas fa-user me-2"></i>基本信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-user-tag me-1 text-muted"></i>用户名
                                    </label>
                                    <input type="text" class="form-control bg-light" value="<?php echo htmlspecialchars($admin['username']); ?>" disabled>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>用户名不可修改
                                    </small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-signature me-1 text-muted"></i>昵称
                                    </label>
                                    <input type="text" class="form-control" name="nickname" value="<?php echo htmlspecialchars($admin['nickname'] ?? ''); ?>" placeholder="请输入昵称">
                                </div>
                            </div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3 fw-bold">
                                <i class="fas fa-address-book me-2"></i>联系信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-envelope me-1 text-muted"></i>邮箱地址
                                    </label>
                                    <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($admin['email'] ?? ''); ?>" placeholder="请输入邮箱地址">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>用于找回密码和接收通知
                                    </small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-phone me-1 text-muted"></i>手机号码
                                    </label>
                                    <input type="tel" class="form-control" name="phone" value="<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>" placeholder="请输入手机号码">
                                    <small class="text-muted">
                                        <i class="fas fa-sms me-1"></i>用于接收短信验证码
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 头像上传 -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3 fw-bold">
                                <i class="fas fa-image me-2"></i>头像设置
                            </h6>
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-upload me-1 text-muted"></i>选择头像文件
                                    </label>
                                    <input type="file" class="form-control" name="avatar" accept="image/*" id="avatarInput">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>支持 JPG、PNG 格式，建议尺寸 200x200 像素，文件大小不超过 2MB
                                    </small>
                                </div>
                                <div class="col-md-4 text-center">
                                    <small class="text-muted d-block mb-2">当前头像预览</small>
                                    <img src="<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMzAiIGZpbGw9IiM2Njc3ZWEiLz4KPHN2ZyB4PSIxNSIgeT0iMTAiIHdpZHRoPSIzMCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgzVjlDMyA2Ljc5IDQuNzkgNSA3IDVIMTdDMTkuMjEgNSAyMSA2Ljc5IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K'; ?>"
                                         alt="头像预览" class="rounded-circle border" width="60" height="60" id="formAvatarPreview"
                                         style="object-fit: cover;">
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-flex flex-wrap gap-2 justify-content-between align-items-center pt-3 border-top">
                            <div>
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-save me-2"></i>保存修改
                                </button>
                                <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>重置
                                </button>
                            </div>
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="fas fa-key me-2"></i>修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">原密码</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                        <small class="text-muted">密码长度不少于6位</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">确认修改</button>
            </div>
        </div>
    </div>
</div>

<style>
/* 头像相关样式 */
.avatar-container {
    transition: all 0.3s ease;
    position: relative;
}

.avatar-container:hover {
    transform: translateY(-2px);
}

.avatar-container:hover .avatar-overlay {
    opacity: 1 !important;
}

.avatar-overlay {
    border-radius: 50%;
    backdrop-filter: blur(2px);
}

/* 卡片样式优化 */
.card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.25rem 1.5rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

/* 统计信息样式 */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* 联系信息样式 */
.contact-info {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    padding: 1rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 布局优化 */
.row {
    display: flex;
    flex-wrap: wrap;
}

.h-100 {
    height: 100% !important;
}

/* 确保两列等高 */
@media (min-width: 992px) {
    .row > [class*="col-"] {
        display: flex;
        flex-direction: column;
    }

    .row > [class*="col-"] > .card {
        flex: 1;
    }
}

/* 响应式优化 */
@media (max-width: 991px) {
    .col-xl-4, .col-xl-8, .col-lg-5, .col-lg-7 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem !important;
    }

    .avatar-container img {
        width: 80px !important;
        height: 80px !important;
    }

    .row.g-0 .col-6 .p-2 {
        padding: 0.5rem !important;
    }
}

/* 默认头像SVG优化 */
#avatarPreview, #formAvatarPreview {
    background: #f8f9fa;
}

/* 表单样式优化 */
.form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    width: 16px;
    text-align: center;
}

.form-control {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.bg-light {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 分组标题样式 */
.card-body h6 {
    position: relative;
    padding-bottom: 0.5rem;
}

.card-body h6::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 头像预览样式 */
#formAvatarPreview {
    border: 2px solid #e3e6f0;
    transition: all 0.3s ease;
}

#formAvatarPreview:hover {
    border-color: #667eea;
    transform: scale(1.05);
}
</style>

<script>
// 头像预览
document.getElementById('avatarInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // 验证文件大小（2MB）
        if (file.size > 2 * 1024 * 1024) {
            showAlert('error', '文件大小不能超过2MB');
            this.value = '';
            return;
        }

        // 验证文件类型
        if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
            showAlert('error', '只支持JPG、PNG格式的图片');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // 同时更新两个头像预览
            document.getElementById('avatarPreview').src = e.target.result;
            document.getElementById('formAvatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// 点击头像选择文件
document.querySelector('.avatar-container').addEventListener('click', function() {
    document.getElementById('avatarInput').click();
});

// 提交个人资料表单
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    
    fetch('/admin/updateProfile', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '更新失败，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 修改密码
function changePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    // 验证密码一致性
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showAlert('error', '两次输入的密码不一致');
        return;
    }
    
    fetch('/admin/changePassword', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '修改失败，请重试');
    });
}

// 重置表单
function resetForm() {
    const form = document.getElementById('profileForm');

    // 重置表单字段到原始值
    form.querySelector('input[name="nickname"]').value = '<?php echo htmlspecialchars($admin['nickname'] ?? ''); ?>';
    form.querySelector('input[name="email"]').value = '<?php echo htmlspecialchars($admin['email'] ?? ''); ?>';
    form.querySelector('input[name="phone"]').value = '<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>';
    form.querySelector('input[name="avatar"]').value = '';

    // 重置头像预览
    const originalAvatar = '<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMzAiIGZpbGw9IiM2Njc3ZWEiLz4KPHN2ZyB4PSIxNSIgeT0iMTAiIHdpZHRoPSIzMCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgzVjlDMyA2Ljc5IDQuNzkgNSA3IDVIMTdDMTkuMjEgNSAyMSA2Ljc5IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K'; ?>';
    document.getElementById('formAvatarPreview').src = originalAvatar;

    showAlert('success', '表单已重置');
}

// 显示提示信息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
