<?php
$page_title = '个人资料';
$current_page = 'profile';

$content = '
<div class="row">
    <!-- 个人信息 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="' . (!empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : '/static/images/default-avatar.svg') . '"
                         alt="头像" class="rounded-circle" width="80" height="80" id="avatarPreview"
                         style="object-fit: cover; border: 3px solid #e3e6f0;">
                </div>
                <h5 class="mb-1">' . htmlspecialchars($admin['nickname'] ?: $admin['username']) . '</h5>
                <p class="text-muted mb-2">@' . htmlspecialchars($admin['username']) . '</p>
                <span class="badge bg-primary">管理员</span>

                <hr class="my-3">

                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted d-block">注册时间</small>
                        <strong>' . date('Y-m-d', strtotime($admin['create_time'])) . '</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">最后登录</small>
                        <strong>' . ($admin['last_login_time'] ? date('m-d H:i', strtotime($admin['last_login_time'])) : '从未登录') . '</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0">编辑资料</h6>
            </div>
            <div class="card-body">
                <form id="profileForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" value="' . htmlspecialchars($admin['username']) . '" disabled>
                            <small class="text-muted">用户名不可修改</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">昵称</label>
                            <input type="text" class="form-control" name="nickname" value="' . htmlspecialchars($admin['nickname'] ?? '') . '" placeholder="请输入昵称">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" name="email" value="' . htmlspecialchars($admin['email'] ?? '') . '" placeholder="请输入邮箱">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-control" name="phone" value="' . htmlspecialchars($admin['phone'] ?? '') . '" placeholder="请输入手机号">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">头像</label>
                        <input type="file" class="form-control" name="avatar" accept="image/*" id="avatarInput">
                        <small class="text-muted">支持 JPG、PNG 格式，建议尺寸不超过 2MB</small>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-1"></i>修改密码
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">原密码</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                        <small class="text-muted">密码长度不少于6位</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">确认修改</button>
            </div>
        </div>
    </div>
</div>

<script>
// 头像预览
document.addEventListener("DOMContentLoaded", function() {
    const avatarInput = document.getElementById("avatarInput");
    if (avatarInput) {
        avatarInput.addEventListener("change", function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件大小（2MB）
                if (file.size > 2 * 1024 * 1024) {
                    alert("文件大小不能超过2MB");
                    this.value = "";
                    return;
                }

                // 验证文件类型
                if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
                    alert("只支持JPG、PNG格式的图片");
                    this.value = "";
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById("avatarPreview").src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // 提交个人资料表单
    const profileForm = document.getElementById("profileForm");
    if (profileForm) {
        profileForm.addEventListener("submit", function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector("button[type=\"submit\"]");
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-1\"></i>保存中...";

            fetch("/admin/updateProfile", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    alert(data.msg);
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert(data.msg);
                }
            })
            .catch(error => {
                console.error("Error:", error);
                alert("更新失败，请重试");
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

// 修改密码
function changePassword() {
    const form = document.getElementById("changePasswordForm");
    const formData = new FormData(form);

    // 验证密码一致性
    const newPassword = formData.get("new_password");
    const confirmPassword = formData.get("confirm_password");

    if (newPassword !== confirmPassword) {
        alert("两次输入的密码不一致");
        return;
    }

    fetch("/admin/changePassword", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            alert(data.msg);
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById("changePasswordModal")).hide();
        } else {
            alert(data.msg);
        }
    })
    .catch(error => {
        console.error("Error:", error);
        alert("修改失败，请重试");
    });
}

// 重置表单
function resetForm() {
    const form = document.getElementById("profileForm");

    // 重置表单字段到原始值
    form.querySelector("input[name=\"nickname\"]").value = "' . htmlspecialchars($admin['nickname'] ?? '') . '";
    form.querySelector("input[name=\"email\"]").value = "' . htmlspecialchars($admin['email'] ?? '') . '";
    form.querySelector("input[name=\"phone\"]").value = "' . htmlspecialchars($admin['phone'] ?? '') . '";
    form.querySelector("input[name=\"avatar\"]").value = "";

    alert("表单已重置");
}
</script>
';

include 'layout.php';
?>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">原密码</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                        <small class="text-muted">密码长度不少于6位</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">确认修改</button>
            </div>
        </div>
    </div>
</div>

<style>
/* 头像相关样式 */
.avatar-container {
    transition: all 0.3s ease;
    position: relative;
}

.avatar-container:hover {
    transform: translateY(-2px);
}

.avatar-container:hover .avatar-overlay {
    opacity: 1 !important;
}

.avatar-overlay {
    border-radius: 50%;
    backdrop-filter: blur(2px);
}

/* 卡片样式优化 */
.card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.25rem 1.5rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

/* 统计信息样式 */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* 联系信息样式 */
.contact-info {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    padding: 1rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 布局优化 */
.row {
    display: flex;
    flex-wrap: wrap;
}

.h-100 {
    height: 100% !important;
}

/* 确保两列等高 */
@media (min-width: 992px) {
    .row > [class*="col-"] {
        display: flex;
        flex-direction: column;
    }

    .row > [class*="col-"] > .card {
        flex: 1;
    }
}

/* 响应式优化 */
@media (max-width: 991px) {
    .col-xl-4, .col-xl-8, .col-lg-5, .col-lg-7 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem !important;
    }

    .avatar-container img {
        width: 80px !important;
        height: 80px !important;
    }

    .row.g-0 .col-6 .p-2 {
        padding: 0.5rem !important;
    }
}

/* 默认头像SVG优化 */
#avatarPreview, #formAvatarPreview {
    background: #f8f9fa;
}

/* 表单样式优化 */
.form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    width: 16px;
    text-align: center;
}

.form-control {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.bg-light {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 分组标题样式 */
.card-body h6 {
    position: relative;
    padding-bottom: 0.5rem;
}

.card-body h6::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 头像预览样式 */
#formAvatarPreview {
    border: 2px solid #e3e6f0;
    transition: all 0.3s ease;
}

#formAvatarPreview:hover {
    border-color: #667eea;
    transform: scale(1.05);
}
</style>

<script>
// 头像预览
document.getElementById('avatarInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // 验证文件大小（2MB）
        if (file.size > 2 * 1024 * 1024) {
            showAlert('error', '文件大小不能超过2MB');
            this.value = '';
            return;
        }

        // 验证文件类型
        if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
            showAlert('error', '只支持JPG、PNG格式的图片');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // 同时更新两个头像预览
            document.getElementById('avatarPreview').src = e.target.result;
            document.getElementById('formAvatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// 点击头像选择文件
document.querySelector('.avatar-container').addEventListener('click', function() {
    document.getElementById('avatarInput').click();
});

// 提交个人资料表单
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    
    fetch('/admin/updateProfile', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '更新失败，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 修改密码
function changePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    // 验证密码一致性
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showAlert('error', '两次输入的密码不一致');
        return;
    }
    
    fetch('/admin/changePassword', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '修改失败，请重试');
    });
}

// 重置表单
function resetForm() {
    const form = document.getElementById('profileForm');

    // 重置表单字段到原始值
    form.querySelector('input[name="nickname"]').value = '<?php echo htmlspecialchars($admin['nickname'] ?? ''); ?>';
    form.querySelector('input[name="email"]').value = '<?php echo htmlspecialchars($admin['email'] ?? ''); ?>';
    form.querySelector('input[name="phone"]').value = '<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>';
    form.querySelector('input[name="avatar"]').value = '';

    // 重置头像预览
    const originalAvatar = '<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMzAiIGZpbGw9IiM2Njc3ZWEiLz4KPHN2ZyB4PSIxNSIgeT0iMTAiIHdpZHRoPSIzMCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgzVjlDMyA2Ljc5IDQuNzkgNSA3IDVIMTdDMTkuMjEgNSAyMSA2Ljc5IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K'; ?>';
    document.getElementById('formAvatarPreview').src = originalAvatar;

    showAlert('success', '表单已重置');
}

// 显示提示信息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
