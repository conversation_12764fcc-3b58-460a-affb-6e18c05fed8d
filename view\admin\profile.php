<?php
$page_title = '个人资料';
$current_page = 'profile';

$content = '
<div class="row justify-content-center">
    <div class="col-lg-8 col-xl-6">
        <!-- 个人信息 -->
        <div class="card mb-3">
            <div class="card-body text-center py-3">
                <div class="mb-2">
                    <img src="' . (!empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : '/static/images/default-avatar.svg') . '"
                         alt="头像" class="rounded-circle avatar-upload" width="80" height="80" id="avatarPreview"
                         style="object-fit: cover; border: 3px solid #e3e6f0; cursor: pointer;">
                </div>
                <small class="text-muted d-block mb-2">点击头像可以更换</small>
                <h5 class="mb-1">' . htmlspecialchars($admin['nickname'] ?: $admin['username']) . '</h5>
                <p class="text-muted mb-2">@' . htmlspecialchars($admin['username']) . '</p>
                <span class="badge bg-primary">管理员</span>

                <hr class="my-2">

                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted d-block">注册时间</small>
                        <strong>' . date('Y-m-d', strtotime($admin['create_time'])) . '</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">最后登录</small>
                        <strong>' . ($admin['last_login_time'] ? date('m-d H:i', strtotime($admin['last_login_time'])) : '从未登录') . '</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑表单 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0">编辑资料</h6>
            </div>
            <div class="card-body">
                <form id="profileForm" enctype="multipart/form-data">
                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">用户名</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" value="' . htmlspecialchars($admin['username']) . '" disabled>
                            <small class="text-muted">用户名不可修改</small>
                        </div>
                    </div>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">昵称</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" name="nickname" value="' . htmlspecialchars($admin['nickname'] ?? '') . '" placeholder="请输入昵称">
                        </div>
                    </div>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">邮箱</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="email" class="form-control" name="email" value="' . htmlspecialchars($admin['email'] ?? '') . '" placeholder="请输入邮箱">
                        </div>
                    </div>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">手机号</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="tel" class="form-control" name="phone" value="' . htmlspecialchars($admin['phone'] ?? '') . '" placeholder="请输入手机号">
                        </div>
                    </div>

                    <!-- 分隔线 -->
                    <hr class="my-4">

                    <!-- 修改密码部分 -->
                    <h6 class="text-muted mb-3">修改密码</h6>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">原密码</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" name="old_password" placeholder="如果不修改密码请留空">
                            <small class="text-muted">如果留空则不修改密码</small>
                        </div>
                    </div>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">新密码</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" name="new_password" placeholder="如果不修改密码请留空" minlength="6">
                            <small class="text-muted">密码长度不少于6位，如果留空则不修改密码</small>
                        </div>
                    </div>

                    <div class="row align-items-center mb-3">
                        <div class="col-sm-3">
                            <label class="form-label mb-sm-0">确认新密码</label>
                        </div>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" name="confirm_password" placeholder="如果不修改密码请留空" minlength="6">
                            <small class="text-muted">请再次输入新密码进行确认</small>
                        </div>
                    </div>

                    <!-- 隐藏的头像上传输入框 -->
                    <input type="file" class="d-none" name="avatar" accept="image/*" id="avatarInput">
                </form>
            </div>
        </div>

        <!-- 保存按钮 -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-primary me-2" onclick="saveProfile()">
                <i class="fas fa-save me-1"></i>保存设置
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetAllForms()">
                <i class="fas fa-undo me-1"></i>重置
            </button>
        </div>
    </div>
</div>



<script>
// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function() {
    // 头像点击上传
    const avatarPreview = document.getElementById("avatarPreview");
    const avatarInput = document.getElementById("avatarInput");

    if (avatarPreview && avatarInput) {
        // 点击头像触发文件选择
        avatarPreview.addEventListener("click", function() {
            avatarInput.click();
        });

        // 文件选择后预览并自动保存
        avatarInput.addEventListener("change", function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件大小（2MB）
                if (file.size > 2 * 1024 * 1024) {
                    alert("文件大小不能超过2MB");
                    this.value = "";
                    return;
                }

                // 验证文件类型
                if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
                    alert("只支持JPG、PNG格式的图片");
                    this.value = "";
                    return;
                }

                // 预览图片
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);

                // 自动上传头像
                uploadAvatar(file);
            }
        });
    }
});

// 上传头像
function uploadAvatar(file) {
    const formData = new FormData();
    formData.append("avatar", file);

    fetch("/admin/updateProfile", {
        method: "POST",
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            console.log("头像上传成功");
            // 不显示提示，静默上传
        } else {
            alert("头像上传失败：" + data.msg);
            // 恢复原头像
            location.reload();
        }
    })
    .catch(error => {
        console.error("Error:", error);
        alert("头像上传失败，请重试");
        location.reload();
    });
}

// 保存所有设置
function saveProfile() {
    const profileForm = document.getElementById("profileForm");
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;

    saveBtn.disabled = true;
    saveBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-1\"></i>保存中...";

    // 检查是否需要修改密码
    const oldPassword = profileForm.querySelector("input[name=\"old_password\"]").value;
    const newPassword = profileForm.querySelector("input[name=\"new_password\"]").value;
    const confirmPassword = profileForm.querySelector("input[name=\"confirm_password\"]").value;

    let promises = [];

    // 保存个人资料
    const profileData = new FormData();
    const nickname = profileForm.querySelector("input[name=\"nickname\"]").value;
    const email = profileForm.querySelector("input[name=\"email\"]").value;
    const phone = profileForm.querySelector("input[name=\"phone\"]").value;

    profileData.append("nickname", nickname);
    profileData.append("email", email);
    profileData.append("phone", phone);

    promises.push(
        fetch("/admin/updateProfile", {
            method: "POST",
            body: profileData
        }).then(response => response.json())
    );

    // 如果填写了密码相关字段，则修改密码
    if (oldPassword || newPassword || confirmPassword) {
        // 验证密码 - 要么全部填写，要么全部留空
        if (oldPassword && !newPassword) {
            alert("请输入新密码");
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
            return;
        }

        if (newPassword && !oldPassword) {
            alert("请输入原密码");
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
            return;
        }

        if (oldPassword && newPassword) {
            if (newPassword.length < 6) {
                alert("新密码长度不能少于6位");
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                return;
            }

            if (newPassword !== confirmPassword) {
                alert("两次输入的密码不一致");
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalText;
                return;
            }

            // 创建密码修改的表单数据
            const passwordData = new FormData();
            passwordData.append("old_password", oldPassword);
            passwordData.append("new_password", newPassword);
            passwordData.append("confirm_password", confirmPassword);

            promises.push(
                fetch("/admin/changePassword", {
                    method: "POST",
                    body: passwordData
                }).then(response => response.json())
            );
        }
    }

    // 执行所有请求
    Promise.all(promises)
        .then(results => {
            let allSuccess = true;
            let messages = [];

            results.forEach((result, index) => {
                if (result.code === 1) {
                    if (index === 0) {
                        messages.push("个人资料更新成功");
                    } else {
                        messages.push("密码修改成功");
                    }
                } else {
                    allSuccess = false;
                    messages.push(result.msg);
                }
            });

            alert(messages.join("\\n"));

            if (allSuccess) {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("保存失败，请重试");
        })
        .finally(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        });
}

// 重置所有表单
function resetAllForms() {
    const profileForm = document.getElementById("profileForm");

    // 重置个人资料表单
    profileForm.querySelector("input[name=\"nickname\"]").value = "' . htmlspecialchars($admin['nickname'] ?? '') . '";
    profileForm.querySelector("input[name=\"email\"]").value = "' . htmlspecialchars($admin['email'] ?? '') . '";
    profileForm.querySelector("input[name=\"phone\"]").value = "' . htmlspecialchars($admin['phone'] ?? '') . '";
    profileForm.querySelector("input[name=\"avatar\"]").value = "";

    // 重置密码字段
    profileForm.querySelector("input[name=\"old_password\"]").value = "";
    profileForm.querySelector("input[name=\"new_password\"]").value = "";
    profileForm.querySelector("input[name=\"confirm_password\"]").value = "";

    // 重置头像预览
    const avatarPreview = document.getElementById("avatarPreview");
    const originalAvatar = "' . (!empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : '/static/images/default-avatar.svg') . '";
    avatarPreview.src = originalAvatar;

    alert("所有表单已重置");
}
</script>
';

include 'layout.php';
?>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">原密码</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                        <small class="text-muted">密码长度不少于6位</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">确认修改</button>
            </div>
        </div>
    </div>
</div>

<style>
/* 头像相关样式 */
.avatar-container {
    transition: all 0.3s ease;
    position: relative;
}

.avatar-container:hover {
    transform: translateY(-2px);
}

.avatar-container:hover .avatar-overlay {
    opacity: 1 !important;
}

.avatar-overlay {
    border-radius: 50%;
    backdrop-filter: blur(2px);
}

/* 卡片样式优化 */
.card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.25rem 1.5rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
}

/* 统计信息样式 */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* 联系信息样式 */
.contact-info {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    padding: 1rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 布局优化 */
.row {
    display: flex;
    flex-wrap: wrap;
}

.h-100 {
    height: 100% !important;
}

/* 确保两列等高 */
@media (min-width: 992px) {
    .row > [class*="col-"] {
        display: flex;
        flex-direction: column;
    }

    .row > [class*="col-"] > .card {
        flex: 1;
    }
}

/* 响应式优化 */
@media (max-width: 991px) {
    .col-xl-4, .col-xl-8, .col-lg-5, .col-lg-7 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem !important;
    }

    .avatar-container img {
        width: 80px !important;
        height: 80px !important;
    }

    .row.g-0 .col-6 .p-2 {
        padding: 0.5rem !important;
    }
}

/* 默认头像SVG优化 */
#avatarPreview, #formAvatarPreview {
    background: #f8f9fa;
}

/* 表单样式优化 */
.form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    width: 16px;
    text-align: center;
}

.form-control {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.bg-light {
    background-color: #f8f9fa !important;
    color: #6c757d;
}

/* 分组标题样式 */
.card-body h6 {
    position: relative;
    padding-bottom: 0.5rem;
}

.card-body h6::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 头像预览样式 */
#formAvatarPreview {
    border: 2px solid #e3e6f0;
    transition: all 0.3s ease;
}

#formAvatarPreview:hover {
    border-color: #667eea;
    transform: scale(1.05);
}
</style>

<script>
// 头像预览
document.getElementById('avatarInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // 验证文件大小（2MB）
        if (file.size > 2 * 1024 * 1024) {
            showAlert('error', '文件大小不能超过2MB');
            this.value = '';
            return;
        }

        // 验证文件类型
        if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
            showAlert('error', '只支持JPG、PNG格式的图片');
            this.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // 同时更新两个头像预览
            document.getElementById('avatarPreview').src = e.target.result;
            document.getElementById('formAvatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// 点击头像选择文件
document.querySelector('.avatar-container').addEventListener('click', function() {
    document.getElementById('avatarInput').click();
});

// 提交个人资料表单
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    
    fetch('/admin/updateProfile', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '更新失败，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 修改密码
function changePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    // 验证密码一致性
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showAlert('error', '两次输入的密码不一致');
        return;
    }
    
    fetch('/admin/changePassword', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '修改失败，请重试');
    });
}

// 重置表单
function resetForm() {
    const form = document.getElementById('profileForm');

    // 重置表单字段到原始值
    form.querySelector('input[name="nickname"]').value = '<?php echo htmlspecialchars($admin['nickname'] ?? ''); ?>';
    form.querySelector('input[name="email"]').value = '<?php echo htmlspecialchars($admin['email'] ?? ''); ?>';
    form.querySelector('input[name="phone"]').value = '<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>';
    form.querySelector('input[name="avatar"]').value = '';

    // 重置头像预览
    const originalAvatar = '<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMzAiIGZpbGw9IiM2Njc3ZWEiLz4KPHN2ZyB4PSIxNSIgeT0iMTAiIHdpZHRoPSIzMCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgzVjlDMyA2Ljc5IDQuNzkgNSA3IDVIMTdDMTkuMjEgNSAyMSA2Ljc5IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K'; ?>';
    document.getElementById('formAvatarPreview').src = originalAvatar;

    showAlert('success', '表单已重置');
}

// 显示提示信息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
