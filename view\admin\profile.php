<?php include 'layout.php'; ?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-circle me-2"></i>个人资料
            </h1>
            <p class="text-muted mb-0">管理您的个人信息和账户设置</p>
        </div>
    </div>

    <div class="row">
        <!-- 个人信息卡片 -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-id-card me-2"></i>基本信息
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <div class="avatar-container position-relative d-inline-block">
                            <img src="<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : '/static/images/default-avatar.png'; ?>" 
                                 alt="头像" class="rounded-circle" width="120" height="120" id="avatarPreview">
                            <div class="avatar-overlay position-absolute top-0 start-0 w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" 
                                 style="background: rgba(0,0,0,0.5); opacity: 0; transition: opacity 0.3s;">
                                <i class="fas fa-camera text-white fs-4"></i>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mb-1"><?php echo htmlspecialchars($admin['nickname'] ?: $admin['username']); ?></h5>
                    <p class="text-muted mb-2">@<?php echo htmlspecialchars($admin['username']); ?></p>
                    <span class="badge bg-success">管理员</span>
                    
                    <div class="mt-4">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="mb-0"><?php echo date('Y-m-d', strtotime($admin['create_time'])); ?></h6>
                                    <small class="text-muted">注册时间</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="mb-0"><?php echo $admin['last_login_time'] ? date('Y-m-d', strtotime($admin['last_login_time'])) : '从未登录'; ?></h6>
                                <small class="text-muted">最后登录</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑表单 -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-edit me-2"></i>编辑资料
                    </h6>
                </div>
                <div class="card-body">
                    <form id="profileForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($admin['username']); ?>" disabled>
                                <small class="text-muted">用户名不可修改</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">昵称</label>
                                <input type="text" class="form-control" name="nickname" value="<?php echo htmlspecialchars($admin['nickname'] ?? ''); ?>" placeholder="请输入昵称">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($admin['email'] ?? ''); ?>" placeholder="请输入邮箱">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">手机号</label>
                                <input type="tel" class="form-control" name="phone" value="<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>" placeholder="请输入手机号">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">头像</label>
                            <input type="file" class="form-control" name="avatar" accept="image/*" id="avatarInput">
                            <small class="text-muted">支持 JPG、PNG 格式，建议尺寸 200x200 像素</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存修改
                            </button>
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                <i class="fas fa-key me-2"></i>修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>修改密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">原密码</label>
                        <input type="password" class="form-control" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" required minlength="6">
                        <small class="text-muted">密码长度不少于6位</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" required minlength="6">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">确认修改</button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-container:hover .avatar-overlay {
    opacity: 1 !important;
    cursor: pointer;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.avatar-container {
    transition: transform 0.3s ease;
}

.avatar-container:hover {
    transform: scale(1.05);
}
</style>

<script>
// 头像预览
document.getElementById('avatarInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// 点击头像选择文件
document.querySelector('.avatar-container').addEventListener('click', function() {
    document.getElementById('avatarInput').click();
});

// 提交个人资料表单
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    
    fetch('/admin/updateProfile', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '更新失败，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 修改密码
function changePassword() {
    const form = document.getElementById('changePasswordForm');
    const formData = new FormData(form);
    
    // 验证密码一致性
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showAlert('error', '两次输入的密码不一致');
        return;
    }
    
    fetch('/admin/changePassword', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showAlert('success', data.msg);
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showAlert('error', data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', '修改失败，请重试');
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
