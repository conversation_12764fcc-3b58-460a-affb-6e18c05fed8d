/* 联系客服悬浮按钮和弹窗功能 */

// 检测是否为移动设备
function isMobileDevice() {
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 处理联系按钮点击事件
function handleContactClick() {
    if (isMobileDevice()) {
        // 移动端显示弹窗
        toggleContactModal();
    }
    // PC端通过CSS hover显示卡片，无需JavaScript处理
}

// 显示联系客服弹窗（移动端）
function toggleContactModal() {
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

// 关闭联系客服弹窗
function closeContactModal() {
    const modal = document.getElementById('contactModal');
    if (modal) {
        modal.classList.remove('show');
        // 等待动画完成后隐藏
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 复制联系方式到剪贴板
function copyContactInfo(text, type) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess(type);
        }).catch(() => {
            fallbackCopyTextToClipboard(text, type);
        });
    } else {
        fallbackCopyTextToClipboard(text, type);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text, type) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showCopySuccess(type);
    } catch (err) {
        console.error('复制失败:', err);
    }
    
    document.body.removeChild(textArea);
}

// 显示复制成功提示
function showCopySuccess(type) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'copy-toast';
    toast.textContent = type + '已复制到剪贴板';
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // 3秒后自动消失
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 点击弹窗外部关闭
    const contactModal = document.getElementById('contactModal');
    if (contactModal) {
        contactModal.addEventListener('click', function(event) {
            if (event.target === contactModal) {
                closeContactModal();
            }
        });
    }
    
    // 为联系方式项添加点击复制功能
    const contactItems = document.querySelectorAll('.contact-modal-item');
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            const text = this.querySelector('span').textContent;
            const type = text.split('：')[0];
            const value = text.split('：')[1];
            
            if (value && value.trim()) {
                copyContactInfo(value.trim(), type);
            }
        });
    });
    
    // ESC键关闭弹窗
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeContactModal();
        }
    });
});
