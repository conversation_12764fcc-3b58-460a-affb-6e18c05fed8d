/**
 * 内容管理JavaScript功能
 */

// 添加内容
function addContent() {
    // 重置表单
    const form = document.getElementById('contentForm');
    form.reset();

    // 移除隐藏的ID字段
    const idInput = form.querySelector('input[name="id"]');
    if (idInput) {
        idInput.remove();
    }

    // 隐藏分类路径显示
    const categoryPathDisplay = document.querySelector('#categoryPathDisplay');
    if (categoryPathDisplay) {
        categoryPathDisplay.style.display = 'none';
    }

    // 设置模态框标题
    document.querySelector('#contentModal .modal-title').textContent = '添加内容';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('contentModal'));
    modal.show();
}

// 验证分类选择和显示分类路径
function validateCategorySelection() {
    const categorySelect = document.querySelector('#categorySelect');
    const categoryPathDisplay = document.querySelector('#categoryPathDisplay');
    const categoryPath = document.querySelector('#categoryPath');

    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const selectedValue = this.value;
            const selectedOption = this.options[this.selectedIndex];

            if (selectedValue) {
                const isLeaf = selectedOption.getAttribute('data-is-leaf') === 'true';

                if (!isLeaf) {
                    // 如果选择的不是叶子节点，显示警告并清空选择
                    if (window.AdminUtils) {
                        AdminUtils.showMessage("该分类不能添加内容，请选择最低级分类", "warning", 3000);
                    } else {
                        alert("该分类不能添加内容，请选择最低级分类");
                    }
                    this.value = '';
                    categoryPathDisplay.style.display = 'none';
                    return;
                }

                // 获取并显示分类路径
                getCategoryPath(selectedValue);
            } else {
                categoryPathDisplay.style.display = 'none';
            }
        });
    }
}

// 获取分类路径
function getCategoryPath(categoryId) {
    fetch(`/admin/getCategoryPath?category_id=${categoryId}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const categoryPathDisplay = document.querySelector('#categoryPathDisplay');
            const categoryPath = document.querySelector('#categoryPath');

            categoryPath.textContent = data.data.category_path;
            categoryPathDisplay.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('获取分类路径错误:', error);
    });
}

// 编辑内容
function editContent(id) {
    console.log("编辑内容:", id);

    // 设置模态框标题
    document.querySelector('#contentModal .modal-title').textContent = '编辑内容';

    // 加载内容数据
    fetch(`/admin/getContent?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const content = data.data;

            // 填充表单数据
            const form = document.getElementById('contentForm');
            form.querySelector('input[name="title"]').value = content.title || '';
            form.querySelector('select[name="category_id"]').value = content.category_id || '';
            form.querySelector('textarea[name="content"]').value = content.content || '';
            form.querySelector('input[name="sort_order"]').value = content.sort_order || 0;
            form.querySelector('input[name="status"]').checked = content.status == 1;

            // 添加隐藏的ID字段用于更新
            let idInput = form.querySelector('input[name="id"]');
            if (!idInput) {
                idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                form.appendChild(idInput);
            }
            idInput.value = content.id;

            // 显示分类路径
            if (content.category_path) {
                const categoryPathDisplay = document.querySelector('#categoryPathDisplay');
                const categoryPath = document.querySelector('#categoryPath');
                categoryPath.textContent = content.category_path;
                categoryPathDisplay.style.display = 'block';
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('contentModal'));
            modal.show();
        } else {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "加载内容失败", "error");
            } else {
                alert(data.msg || "加载内容失败");
            }
        }
    })
    .catch(error => {
        console.error('加载内容错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误，请稍后重试", "error");
        } else {
            alert("网络错误，请稍后重试");
        }
    });
}

// 查看内容
function viewContent(id) {
    console.log("查看内容:", id);

    // 获取内容详情
    fetch(`/admin/getContent?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            const content = data.data;

            // 创建查看模态框
            const modalHtml = `
                <div class="modal fade" id="viewContentModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">查看内容</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-3"><strong>标题：</strong></div>
                                    <div class="col-9">${content.title}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-3"><strong>分类路径：</strong></div>
                                    <div class="col-9">${content.category_path || '未分类'}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-3"><strong>状态：</strong></div>
                                    <div class="col-9">
                                        <span class="badge ${content.status == 1 ? 'bg-success' : 'bg-secondary'}">
                                            ${content.status == 1 ? '启用' : '禁用'}
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-3"><strong>排序：</strong></div>
                                    <div class="col-9">${content.sort_order}</div>
                                </div>
                                <div class="row">
                                    <div class="col-3"><strong>内容：</strong></div>
                                    <div class="col-9">
                                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px; border-radius: 4px; background: #f8f9fa;">
                                            ${content.content.replace(/\n/g, '<br>')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="editContent(${content.id}); bootstrap.Modal.getInstance(document.getElementById('viewContentModal')).hide();">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('viewContentModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('viewContentModal'));
            modal.show();
        } else {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "获取内容失败", "error");
            } else {
                alert(data.msg || "获取内容失败");
            }
        }
    })
    .catch(error => {
        console.error('查看内容错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误，请稍后重试", "error");
        } else {
            alert("网络错误，请稍后重试");
        }
    });
}

// 切换内容状态（操作列按钮使用）- 已废弃，使用页面内的函数
// function toggleContentStatus(id, status) {
//     performToggleStatus(id, status);
// }

// 执行状态切换
function performToggleStatus(id, status) {
    fetch('/admin/toggleContentStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${encodeURIComponent(id)}&status=${encodeURIComponent(status)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "状态更新成功", "success");
            } else {
                alert(data.msg || "状态更新成功");
            }

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "状态更新失败", "error");
            } else {
                alert(data.msg || "状态更新失败");
            }
        }
    })
    .catch(error => {
        console.error('状态更新错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误，请稍后重试", "error");
        } else {
            alert("网络错误，请稍后重试");
        }
    });
}

// 删除内容
function deleteContent(id) {
    if (window.AdminUtils) {
        AdminUtils.confirm("确定要删除这个内容吗？删除后无法恢复！", function() {
            performDeleteContent(id);
        });
    } else {
        if (confirm("确定要删除这个内容吗？删除后无法恢复！")) {
            performDeleteContent(id);
        }
    }
}

// 执行删除
function performDeleteContent(id) {
    fetch('/admin/deleteContent', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${encodeURIComponent(id)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "内容删除成功", "success");
            } else {
                alert(data.msg || "内容删除成功");
            }

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "删除失败", "error");
            } else {
                alert(data.msg || "删除失败");
            }
        }
    })
    .catch(error => {
        console.error('删除内容错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误，请稍后重试", "error");
        } else {
            alert("网络错误，请稍后重试");
        }
    });
}

// 批量删除
function batchDelete() {
    const selected = document.querySelectorAll(".form-check-input:checked");
    if (selected.length === 0) {
        if (window.AdminUtils) {
            AdminUtils.showMessage("请选择要删除的内容", "warning");
        } else {
            alert("请选择要删除的内容");
        }
        return;
    }
    
    if (window.AdminUtils) {
        AdminUtils.confirm(`确定要删除选中的 ${selected.length} 个内容吗？`, function() {
            console.log("批量删除内容");
            // TODO: 实现批量删除功能
            AdminUtils.showMessage("批量删除成功！", "success");
        });
    } else {
        if (confirm(`确定要删除选中的 ${selected.length} 个内容吗？`)) {
            console.log("批量删除内容");
            alert("批量删除成功！");
        }
    }
}

// 导出内容
function exportContents() {
    console.log("导出内容");
    if (window.AdminUtils) {
        AdminUtils.showMessage("导出功能开发中...", "info");
    } else {
        alert("导出功能开发中...");
    }
}

// 更新内容排序
function updateContentSort(id, sortOrder) {
    const input = document.querySelector(`input[data-id="${id}"]`);
    const originalValue = input.dataset.original;
    
    // 如果值没有变化，不发送请求
    if (sortOrder == originalValue) {
        return;
    }
    
    // 验证输入值
    if (!sortOrder || isNaN(sortOrder) || sortOrder < 0) {
        if (window.AdminUtils) {
            AdminUtils.showMessage("请输入有效的排序值（大于等于0的数字）", "warning", 3000);
        } else {
            alert("请输入有效的排序值（大于等于0的数字）");
        }
        input.value = originalValue;
        return;
    }
    
    // 添加加载状态
    input.classList.add('loading');
    input.disabled = true;
    
    fetch('/admin/updateContentSort', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${encodeURIComponent(id)}&sort_order=${encodeURIComponent(sortOrder)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "排序更新成功", "success", 2000);
            }
            
            // 更新原始值
            input.dataset.original = sortOrder;
        } else {
            if (window.AdminUtils) {
                AdminUtils.showMessage(data.msg || "排序更新失败", "error", 3000);
            } else {
                alert(data.msg || "排序更新失败");
            }
            // 恢复原始值
            input.value = originalValue;
        }
    })
    .catch(error => {
        console.error('更新排序错误:', error);
        if (window.AdminUtils) {
            AdminUtils.showMessage("网络错误，请稍后重试", "error", 3000);
        } else {
            alert("网络错误，请稍后重试");
        }
        // 恢复原始值
        input.value = originalValue;
    })
    .finally(() => {
        input.classList.remove('loading');
        input.disabled = false;
    });
}

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.form-check-input[type="checkbox"]:not(#selectAll)');
    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表格（如果TableManager存在）
    if (window.TableManager) {
        TableManager.init("contentsTable", { sortable: true });
    }

    // 初始化分类选择验证
    validateCategorySelection();
    
    // 绑定表单提交事件
    const contentForm = document.getElementById('contentForm');
    if (contentForm) {
        contentForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // 转换为URL编码格式
            const urlEncodedData = new URLSearchParams();
            for (const [key, value] of formData.entries()) {
                urlEncodedData.append(key, value);
            }

            // 提交到后端
            fetch('/admin/saveContent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: urlEncodedData.toString()
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    if (window.AdminUtils) {
                        AdminUtils.showMessage(data.msg || "内容保存成功！", "success");
                    } else {
                        alert(data.msg || "内容保存成功！");
                    }

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById("contentModal"));
                    if (modal) {
                        modal.hide();
                    }

                    // 刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.AdminUtils) {
                        AdminUtils.showMessage(data.msg || "保存失败", "error");
                    } else {
                        alert(data.msg || "保存失败");
                    }
                }
            })
            .catch(error => {
                console.error('保存内容错误:', error);
                if (window.AdminUtils) {
                    AdminUtils.showMessage("网络错误，请稍后重试", "error");
                } else {
                    alert("网络错误，请稍后重试");
                }
            });
        });
    }
});

// 改变每页显示条数
function changePageSize(pageSize) {
    const url = new URL(window.location.href);
    url.searchParams.set('page_size', pageSize);
    url.searchParams.set('page', 1); // 重置到第一页
    window.location.href = url.toString();
}
