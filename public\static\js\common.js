/**
 * 公共JavaScript函数库
 */

// 工具函数
const Utils = {
    /**
     * 显示消息提示
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        };
        
        const alertHtml = `
            <div class="alert ${alertClass[type]} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 创建消息容器（如果不存在）
        let messageContainer = document.getElementById('message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'message-container';
            messageContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            document.body.appendChild(messageContainer);
        }
        
        // 添加消息
        const alertElement = document.createElement('div');
        alertElement.innerHTML = alertHtml;
        messageContainer.appendChild(alertElement.firstElementChild);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                const alert = messageContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, duration);
        }
    },
    
    /**
     * AJAX请求封装
     */
    ajax: function(options) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: 10000
        };
        
        const config = Object.assign({}, defaults, options);
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.timeout = config.timeout;
            xhr.ontimeout = () => reject(new Error('请求超时'));
            xhr.onerror = () => reject(new Error('网络错误'));
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            
            xhr.open(config.method, config.url);
            
            // 设置请求头
            for (const header in config.headers) {
                xhr.setRequestHeader(header, config.headers[header]);
            }
            
            // 发送数据
            if (config.data) {
                if (config.headers['Content-Type'] === 'application/json') {
                    xhr.send(JSON.stringify(config.data));
                } else if (config.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
                    // 处理form数据
                    if (typeof config.data === 'object') {
                        const formData = new URLSearchParams();
                        for (const key in config.data) {
                            formData.append(key, config.data[key]);
                        }
                        xhr.send(formData.toString());
                    } else {
                        xhr.send(config.data);
                    }
                } else {
                    xhr.send(config.data);
                }
            } else {
                xhr.send();
            }
        });
    },
    
    /**
     * 格式化日期
     */
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 防抖函数
     */
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    },
    
    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 表单验证
const Validator = {
    /**
     * 验证卡密格式
     */
    validateCardNumber: function(cardNumber) {
        if (!cardNumber || cardNumber.trim() === '') {
            return { valid: false, message: '请输入卡密' };
        }
        
        const trimmed = cardNumber.trim();
        if (trimmed.length < 4) {
            return { valid: false, message: '卡密长度不能少于4位' };
        }
        
        return { valid: true, message: '' };
    },
    
    /**
     * 验证邮箱格式
     */
    validateEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return { valid: false, message: '邮箱格式不正确' };
        }
        return { valid: true, message: '' };
    },
    
    /**
     * 验证手机号格式
     */
    validatePhone: function(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
            return { valid: false, message: '手机号格式不正确' };
        }
        return { valid: true, message: '' };
    }
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化提示工具
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 自动隐藏警告消息
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            }
        });
    }, 3000);
});
