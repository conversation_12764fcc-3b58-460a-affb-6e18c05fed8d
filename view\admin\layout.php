<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <link href="/static/css/admin.css" rel="stylesheet">
    <?php if (isset($extra_css)): ?>
        <?php foreach ($extra_css as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <div class="admin-wrapper">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-shield-alt"></i>
                    <span class="brand-text">卡密兑换系统</span>
                </div>
                <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="sidebar-menu">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>控制台</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'cards' ? 'active' : ''; ?>" href="/admin/cards">
                            <i class="fas fa-credit-card"></i>
                            <span>卡密管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'categories' ? 'active' : ''; ?>" href="/admin/categories">
                            <i class="fas fa-folder-tree"></i>
                            <span>分类管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page === 'contents' ? 'active' : ''; ?>" href="/admin/contents">
                            <i class="fas fa-file-alt"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo in_array($current_page, ['settings', 'card-settings', 'frontend-settings']) ? 'active' : ''; ?> dropdown-toggle"
                           href="#" data-bs-toggle="collapse" data-bs-target="#settingsSubmenu"
                           aria-expanded="<?php echo in_array($current_page, ['settings', 'card-settings', 'frontend-settings']) ? 'true' : 'false'; ?>">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                        <div class="collapse <?php echo in_array($current_page, ['settings', 'card-settings', 'frontend-settings']) ? 'show' : ''; ?>" id="settingsSubmenu">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $current_page === 'settings' ? 'active' : ''; ?>" href="/admin/settings">
                                        <i class="fas fa-sliders-h"></i>
                                        <span>基本设置</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $current_page === 'card-settings' ? 'active' : ''; ?>" href="/admin/card-settings">
                                        <i class="fas fa-credit-card"></i>
                                        <span>卡密设置</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $current_page === 'frontend-settings' ? 'active' : ''; ?>" href="/admin/frontend-settings">
                                        <i class="fas fa-desktop"></i>
                                        <span>前端设置</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="sidebar-toggle d-lg-none" id="sidebarToggleTop">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title"><?php echo $page_title ?? '控制台'; ?></h1>
                </div>
                
                <div class="header-right">
                    <div class="user-info-display d-flex align-items-center">
                        <img src="<?php echo !empty($admin['avatar']) ? htmlspecialchars($admin['avatar']) : '/static/images/default-avatar.svg'; ?>"
                             alt="头像" class="user-avatar-simple me-2" width="32" height="32">
                        <div class="user-details me-3">
                            <span class="user-name-simple"><?php echo isset($admin['nickname']) && !empty($admin['nickname']) ? htmlspecialchars($admin['nickname']) : htmlspecialchars($admin['username'] ?? '管理员'); ?></span>
                            <span class="user-separator">|</span>
                            <span class="user-role-simple">管理员</span>
                        </div>
                        <div class="user-actions d-flex align-items-center">
                            <a href="/admin/profile" class="user-action-link me-2" title="个人资料">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="/admin/logout" class="user-action-link text-danger" title="退出登录">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">
                <?php echo $content ?? ''; ?>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin.js"></script>
    
    <?php if (isset($extra_js)): ?>
        <?php foreach ($extra_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
